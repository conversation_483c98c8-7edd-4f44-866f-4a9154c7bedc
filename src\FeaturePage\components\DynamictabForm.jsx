import { <PERSON><PERSON>, Card, Col, Form, Input, Row, Select, Tooltip } from "antd";
import { MinusCircle, Plus, HelpCircle } from "lucide-react";
import React, { useEffect, useMemo, memo } from "react";

// Memoized form item components for better performance
const FormItemWithTooltip = memo(({ label, tooltip, children, ...props }) => (
  <Form.Item
    label={
      tooltip ? (
        <span>
          {label}{" "}
          <Tooltip title={tooltip}>
            <HelpCircle className="tw-w-4 tw-h-4 tw-inline tw-mb-1" />
          </Tooltip>
        </span>
      ) : (
        label
      )
    }
    {...props}
  >
    {children}
  </Form.Item>
));

const SectionItem = memo(({ field, remove, form }) => (
  <Card
    key={field.key}
    size="small"
    title={`Section ${field.name + 1}`}
    extra={
      <MinusCircle
        className="tw-text-red-500 tw-cursor-pointer"
        onClick={() => remove(field.name)}
      />
    }
  >
    <Form.Item
      {...field}
      label="Section Label"
      name={[field.name, "sectionLabel"]}
      rules={[{ required: true, message: "Section label is required" }]}
    >
      <Input placeholder="Enter section label" />
    </Form.Item>
    <SectionItemsList field={field} form={form} />
  </Card>
));

const SectionItemsList = memo(({ field, form }) => (
  <Form.List name={[field.name, "sectionItems"]}>
    {(fields, { add, remove }) => (
      <div className="">
        <h3 className="tw-font-semibold tw-text-lg tw-mb-2">Section Items</h3>

        {fields.map((subField) => (
          <Row key={subField.key} gutter={[16, 0]}>
            <Col span={10}>
              <Form.Item
                {...subField}
                name={[subField.name, "label"]}
                rules={[{ required: true, message: "Label is required" }]}
              >
                <Input placeholder="Enter label" />
              </Form.Item>
            </Col>
            <Col span={10}>
              <Form.Item
                {...subField}
                name={[subField.name, "slug"]}
                rules={[
                  { required: true, message: "Slug is required" },
                  {
                    pattern: /^[a-z0-9-]+$/,
                    message: "Only lowercase letters, numbers, and hyphens",
                  },
                ]}
              >
                <Input placeholder="Enter slug" />
              </Form.Item>
            </Col>
            <Col span={4} className="tw-mt-2">
              <MinusCircle
                className="tw-text-red-500 tw-cursor-pointer"
                onClick={() => remove(subField.name)}
              />
            </Col>
          </Row>
        ))}
        <Button
          type="dashed"
          onClick={() => add({ label: "", slug: "" })}
          block
          icon={<Plus className="tw-w-4 tw-h-4" />}
        >
          Add Section Item
        </Button>
      </div>
    )}
  </Form.List>
));

const DynamictabForm = memo(
  ({ tab, currentPage, onValuesChange, form, onFormInstanceCreated }) => {
    // Create form instance if not provided
    const [internalForm] = Form.useForm();
    const formInstance = form || internalForm;

    // Notify parent about form instance creation
    useEffect(() => {
      if (onFormInstanceCreated && !form) {
        console.log("Form instance created for tab:", tab);
        onFormInstanceCreated(tab, formInstance);
      }
    }, [tab, formInstance, onFormInstanceCreated, form]);
    // Memoize the validation rules
    const validationRules = useMemo(
      () => ({
        sitemapLabel: [
          { required: true, message: "Sitemap label is required" },
          { max: 100, message: "Sitemap label cannot exceed 100 characters" },
        ],
        navigationType: [
          { required: true, message: "Navigation type is required" },
        ],
        columnCount: [
          { required: true, message: "Column count is required" },
          {
            type: "number",
            min: 1,
            max: 6,
            message: "Column count must be between 1 and 6",
          },
        ],
      }),
      []
    );

    // Memoize navigation options
    const navigationOptions = useMemo(
      () => [
        { value: "Single Column", label: "Single Column" },
        { value: "Multi Column", label: "Multi Column" },
      ],
      []
    );

    // Efficient form initialization
    useEffect(() => {
      if (formInstance && currentPage) {
        const formValues = {
          sitemapLabel: currentPage.sitemapLabel,
          navigationType: currentPage.navigationType,
          columnCount: currentPage.columnCount,
          sections: currentPage.sections?.map((section) => ({
            ...section,
            sectionItems: section.sectionItems?.map((item) => ({
              ...item,
              slug: item.slug?.toLowerCase(),
            })),
          })),
        };
        formInstance.setFieldsValue(formValues);
      }
    }, [tab, currentPage, formInstance]);

    // Debounced form change handler
    const handleFormChange = useMemo(() => {
      let timeoutId;
      return (changedValues, allValues) => {
        if (timeoutId) clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          // Process form values before sending to parent
          const processedValues = {
            ...allValues,
            sections: allValues.sections?.map((section) => ({
              ...section,
              sectionItems: section.sectionItems?.map((item) => ({
                ...item,
                slug: item.slug?.toLowerCase(),
              })),
            })),
          };
          onValuesChange(changedValues, processedValues);
        }, 300);
      };
    }, [onValuesChange]);

    return (
      <Form
        form={formInstance}
        name={`dynamic_form_${tab}`}
        layout="vertical"
        size="large"
        onValuesChange={handleFormChange}
        initialValues={currentPage}
        className=""
      >
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={8}>
            <FormItemWithTooltip
              label="Sitemap Label"
              name="sitemapLabel"
              tooltip="The label that will appear in the sitemap"
              rules={validationRules.sitemapLabel}
            >
              <Input placeholder="Enter sitemap label" />
            </FormItemWithTooltip>
          </Col>
          <Col xs={24} lg={8}>
            <FormItemWithTooltip
              label="Navigation Type"
              name="navigationType"
              tooltip="Select how the page content will be laid out"
              rules={validationRules.navigationType}
            >
              <Select
                placeholder="Select navigation type"
                options={navigationOptions}
              />
            </FormItemWithTooltip>
          </Col>
          <Col xs={24} lg={8}>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.navigationType !== currentValues.navigationType
              }
            >
              {({ getFieldValue }) =>
                getFieldValue("navigationType")?.includes("Multi Column") && (
                  <FormItemWithTooltip
                    label="Column Count"
                    name="columnCount"
                    tooltip="Number of columns in the grid layout"
                    rules={validationRules.columnCount}
                  >
                    <Input type="number" placeholder="Enter column count" />
                  </FormItemWithTooltip>
                )
              }
            </Form.Item>
          </Col>
        </Row>

        <Form.List name="sections">
          {(fields, { add, remove }) => (
            <div className="tw-space-y-4">
              {fields.map((field) => (
                <SectionItem
                  key={field.key}
                  field={field}
                  remove={remove}
                  form={formInstance}
                />
              ))}
              <Button
                type="dashed"
                onClick={() => add({ sectionLabel: "", sectionItems: [] })}
                block
                icon={<Plus className="tw-w-4 tw-h-4" />}
              >
                Add Section
              </Button>
            </div>
          )}
        </Form.List>
      </Form>
    );
  }
);

FormItemWithTooltip.displayName = "FormItemWithTooltip";
SectionItem.displayName = "SectionItem";
SectionItemsList.displayName = "SectionItemsList";
DynamictabForm.displayName = "DynamictabForm";

export default DynamictabForm;
