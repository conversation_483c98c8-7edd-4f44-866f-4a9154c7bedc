/**
 * Image Content Replacer Utility
 * Handles automatic replacement of image placeholders in HTML content
 * with uploaded image paths and alt attributes
 */

/**
 * Replace image placeholders in HTML content with actual image paths
 * @param {string} htmlContent - The HTML content containing placeholders
 * @param {Object} contentJSON - The content data with image paths
 * @param {Array} mediaFiles - Array of uploaded media files
 * @returns {string} - HTML content with replaced image placeholders
 */
export const replaceImagePlaceholders = (htmlContent, contentJSON, mediaFiles = []) => {
  if (!htmlContent) return htmlContent;

  let processedHTML = htmlContent;

  // Replace ${key} placeholders in src attributes
  if (contentJSON && typeof contentJSON === "object") {
    Object.keys(contentJSON).forEach((key) => {
      const value = contentJSON[key];
      
      if (typeof value === "string" && value.startsWith("/uploads/")) {
        // This is an image path, replace both src and alt
        const imageKey = key;
        
        // Replace src attribute placeholders
        const srcRegex = new RegExp(`src=["']\\$\\{${imageKey}\\}["']`, "g");
        processedHTML = processedHTML.replace(srcRegex, `src="${value}"`);
        
        // Replace alt attribute placeholders with image name
        const imageName = getImageNameFromPath(value);
        const altRegex = new RegExp(`alt=["']\\$\\{${imageKey}\\}["']`, "g");
        processedHTML = processedHTML.replace(altRegex, `alt="${imageName}"`);
        
        // Also replace standalone placeholders
        const standaloneRegex = new RegExp(`\\$\\{${imageKey}\\}`, "g");
        processedHTML = processedHTML.replace(standaloneRegex, value);
      }
    });
  }

  return processedHTML;
};

/**
 * Extract image name from file path for alt attributes
 * @param {string} imagePath - The image file path
 * @returns {string} - Clean image name for alt attribute
 */
export const getImageNameFromPath = (imagePath) => {
  if (!imagePath) return "";
  
  // Extract filename from path
  const filename = imagePath.split("/").pop();
  
  // Remove file extension and clean up
  const nameWithoutExt = filename.replace(/\.[^/.]+$/, "");
  
  // Convert to readable format (replace underscores/hyphens with spaces, capitalize)
  return nameWithoutExt
    .replace(/[_-]/g, " ")
    .replace(/\b\w/g, (char) => char.toUpperCase());
};

/**
 * Auto-detect image placeholders in HTML content
 * @param {string} htmlContent - The HTML content to analyze
 * @returns {Array} - Array of detected image placeholder keys
 */
export const detectImagePlaceholders = (htmlContent) => {
  if (!htmlContent) return [];

  const placeholders = [];
  
  // Find src="${key}" patterns
  const srcMatches = htmlContent.match(/src=["']\$\{([^}]+)\}["']/g);
  if (srcMatches) {
    srcMatches.forEach(match => {
      const key = match.match(/\$\{([^}]+)\}/)[1];
      if (!placeholders.includes(key)) {
        placeholders.push(key);
      }
    });
  }
  
  // Find alt="${key}" patterns
  const altMatches = htmlContent.match(/alt=["']\$\{([^}]+)\}["']/g);
  if (altMatches) {
    altMatches.forEach(match => {
      const key = match.match(/\$\{([^}]+)\}/)[1];
      if (!placeholders.includes(key)) {
        placeholders.push(key);
      }
    });
  }
  
  // Find standalone ${key} patterns that might be images
  const standaloneMatches = htmlContent.match(/\$\{([^}]+)\}/g);
  if (standaloneMatches) {
    standaloneMatches.forEach(match => {
      const key = match.match(/\$\{([^}]+)\}/)[1];
      if (key.toLowerCase().includes('image') || 
          key.toLowerCase().includes('img') ||
          key.toLowerCase().includes('photo') ||
          key.toLowerCase().includes('picture') ||
          key.toLowerCase().includes('hero') ||
          key.toLowerCase().includes('banner') ||
          key.toLowerCase().includes('logo')) {
        if (!placeholders.includes(key)) {
          placeholders.push(key);
        }
      }
    });
  }
  
  return placeholders;
};

/**
 * Generate content structure for image placeholders
 * @param {Array} imagePlaceholders - Array of image placeholder keys
 * @param {Array} mediaFiles - Array of available media files
 * @returns {Object} - Content object with matched images
 */
export const generateImageContent = (imagePlaceholders, mediaFiles = []) => {
  const content = {};
  
  imagePlaceholders.forEach(placeholder => {
    // Try to find matching media file
    const matchingFile = mediaFiles.find(file => {
      const fileKey = file.originalName.replace(/\.[^/.]+$/, ""); // Remove extension
      return fileKey.toLowerCase() === placeholder.toLowerCase() ||
             placeholder.toLowerCase().includes(fileKey.toLowerCase()) ||
             fileKey.toLowerCase().includes(placeholder.toLowerCase());
    });
    
    if (matchingFile) {
      content[placeholder] = matchingFile.path;
    } else {
      // Set empty string for manual assignment
      content[placeholder] = "";
    }
  });
  
  return content;
};

/**
 * Update existing content with new image assignments
 * @param {Object} existingContent - Current content JSON
 * @param {string} imageKey - The content key to update
 * @param {string} imagePath - The image path to assign
 * @returns {Object} - Updated content object
 */
export const updateContentWithImage = (existingContent, imageKey, imagePath) => {
  const updatedContent = { ...existingContent };
  
  const updateNestedContent = (obj, key, path) => {
    if (typeof obj === 'object' && obj !== null) {
      Object.keys(obj).forEach(objKey => {
        if (objKey === key) {
          obj[objKey] = path;
        } else if (typeof obj[objKey] === 'object' && obj[objKey] !== null) {
          updateNestedContent(obj[objKey], key, path);
        }
      });
    }
  };
  
  updateNestedContent(updatedContent, imageKey, imagePath);
  return updatedContent;
};

/**
 * Extract image key from filename (remove extension)
 * @param {string} filename - The image filename
 * @returns {string} - Key for content matching
 */
export const extractImageKey = (filename) => {
  return filename.replace(/\.[^/.]+$/, "");
};

/**
 * Check if a content key is likely for images
 * @param {string} key - The content key to check
 * @returns {boolean} - True if key is likely for images
 */
export const isImageContentKey = (key) => {
  const lowerKey = key.toLowerCase();
  return lowerKey.includes('image') || 
         lowerKey.includes('img') ||
         lowerKey.includes('photo') ||
         lowerKey.includes('picture') ||
         lowerKey.includes('hero') ||
         lowerKey.includes('banner') ||
         lowerKey.includes('logo') ||
         lowerKey.includes('avatar') ||
         lowerKey.includes('thumbnail') ||
         lowerKey.includes('icon');
};
