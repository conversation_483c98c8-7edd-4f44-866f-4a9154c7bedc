import React, { useEffect, useState } from "react";
import TabList from "../../components/common/TabList";
import TemplatePreview from "../../components/Templates/Component/TemplatePreview";
import { DndProvider } from "react-dnd";
import MediaBar from "../../components/Templates/Component/MediaBar";
import ContentCollapseBar from "../../components/Templates/Component/ContentCollapseBar";
import { ChevronDown, Image, ListCollapse, Palette } from "lucide-react";
import useDebounce from "../../hooks/useDebounce";
import { HTML5Backend } from "react-dnd-html5-backend";
import PageListPreview from "./PageListPreview";
import BrandingDetailsCard from "./BrandingDetailsCard";
import { predefinedColors } from "../../util/content";
import { buildVarsForAllSlugs, findAndReplaceVariable } from "./function";
import { Card, Collapse } from "antd";
import { generateGlobalPreviewHTML } from "../../components/Components/content";

export const replaceTemplateContent = ({
  page,
  tempContent,
  content,
  slug,
  mediaFiles,
  allVariables,
}) => {
  const pageVariable = {};
  const pageName = slug?.label || page.name;
  // console.log({ page, tempContent, content, slug, mediaFiles }, "page");
  // console.log(JSON.parse(JSON.stringify(allVariables)), "allVariables");
  page.components = page?.components?.map((component) => {
    const comVariable = {};
    component.html_content_withValue = component.html_content;
    // const extraVariable = [];
    component?.placeholders?.map((placeholder) => {
      let placeHolderValue =
        allVariables?.[pageName]?.[component.name]?.[placeholder] ||
        content?.[page.name]?.[component.name]?.[placeholder] ||
        tempContent?.[page.name]?.[component.name]?.[placeholder] ||
        "";
      // console.log(placeholder, placeHolderValue, "placeHolderValue");
      if (Array.isArray(placeHolderValue)) {
        const repeatedCom = placeHolderValue?.map((item) => {
          // console.log(
          //   component?.repeatedComponent?.html_content,
          //   "html_content",
          //   item
          // );
          return findAndReplaceVariable({
            str: component?.repeatedComponent?.html_content,
            content: item,
            mediaFiles,
            // comVariable,
          });
        });
        // console.log(repeatedCom, "repeatedCom");
        component.html_content_withValue =
          component?.html_content_withValue.replace(
            new RegExp(`\\$\\{${placeholder}\\}`, "g"),
            repeatedCom.map((item) => item.str).join("")
          );
      } else {
        if (placeHolderValue != null && placeHolderValue != undefined) {
          component.html_content_withValue =
            component?.html_content_withValue.replace(
              new RegExp(`\\$\\{${placeholder}\\}`, "g"),
              placeHolderValue
            );
        }
      }

      comVariable[placeholder] = placeHolderValue;
    });
    let pageContent = {
      ...(content?.[page.name]?.[component.name] || {}),
      ...(allVariables?.[pageName]?.[component.name] || {}),
    };

    const response = findAndReplaceVariable({
      str: component?.html_content_withValue,
      content: { ...pageContent, slug: slug?.label },
      mediaFiles,
      comVariable,
    });
    if (response) {
      component.html_content_withValue = response.str;
      if (response?.extraVariable.length) {
        // extraVariable?.concat(response.extraVariable);
        component.extraPlaceholders = response?.extraVariable;
      }
    }
    pageVariable[component.name] = comVariable;
    return component;
  });
  page.full_page_content = generateGlobalPreviewHTML({
    type: "page",
    data: page?.components,
    components: page?.components,
    customCSS: page.custom_css,
    customJS: page.custom_js,
    title: page?.name,
    isHtml: true,
  });

  page.variable = pageVariable;
  return page;
};

const BradingTab = ({
  templateObj,
  setTemplateObj,
  formData,
  setFormData,
  onCancel,
  handleSubmit,
  saving,
  pages,
  components = [],
  dynamicpages = {},
}) => {
  // const [contentJSON, setContentJSON] = useState(formData?.contentJSON ?? {});
  // const [filterContentJson, setFilterContetnJson] = useState({});
  const [fileList, setFileList] = useState(formData?.FileList ?? []);
  const [bradingDetails, setBradingDetails] = useState({
    primaryColor: "#3B82F6",
    secondaryColor: "#3B82F6",
    backgroundColor: "#ffffff",
    textColor: "#000000",
    borderColor: "#d9d9d9",
    logoFile: null,
    faviconFile: null,
  });
  const [previewMode, setPreviewMode] = useState("contents");
  const [reset, setReset] = useState(false);
  // const debouncedFilterContentJson = useDebounce(filterContentJson, 300);
  console.log(bradingDetails);
  const [webContentJson, setWebContetnJson] = useState({});
  const [pageList, setPageList] = useState([]);
  console.log({ pageList, old: templateObj?.pages });
  const handleBrandingChange = (patch) => {
    setBradingDetails((prev) => ({ ...prev, ...patch }));
  };
  useEffect(() => {
    setWebContetnJson((pr) => {
      const allVariables = JSON.parse(JSON.stringify(pr));

      setPageList((pr) => {
        const pages = [];

        templateObj?.pages?.map((templatePage) => {
          const pageObj = JSON.parse(JSON.stringify(templatePage));
          if (pageObj?.type == "dynamic") {
            if (Object.keys(dynamicpages)?.length) {
              const page = dynamicpages[pageObj?.name];
              const sections = page?.sections;
              if (sections?.length) {
                sections?.map((el) => {
                  const slugArr = el?.sectionItems;
                  slugArr?.map((slugObj) => {
                    const dynamicPageObj = JSON.parse(JSON.stringify(pageObj));
                    const pageName = slugObj?.label;

                    pages?.push({
                      ...replaceTemplateContent({
                        page: dynamicPageObj,
                        tempContent: templateObj?.templateContentJSON,
                        content: templateObj?.contentJSON,
                        slug: slugObj,
                        mediaFiles: templateObj?.FileList,
                        allVariables,
                      }),
                      url: `${dynamicPageObj.url}/${slugObj?.slug}`,
                      name: slugObj?.label,
                    });
                    allVariables[pageName] = {
                      ...dynamicPageObj.variable,
                    };
                  });
                });
              }
            }
          } else {
            replaceTemplateContent({
              page: pageObj,
              tempContent: templateObj?.templateContentJSON,
              content: templateObj?.contentJSON,
              mediaFiles: templateObj?.FileList,
              allVariables,
            });
            allVariables[pageObj.name] = { ...pageObj.variable };
            pages.push(pageObj);
          }
        });
        return pages;
      });
      return allVariables;
    });
  }, [reset]);

  const contentChangeHandler = ({ variableName, e, path }) => {
    // console.log(
    //   { variableName, value: e.target.value, path },
    //   "contentChangeHandler"
    // );
    if (path?.length < 2) return console.error("path is less than 2");
    setWebContetnJson((prev) => {
      // console.log(prev, "prev");
      const newValue = JSON.parse(JSON.stringify(prev));
      let current = newValue;
      path?.map((key) => {
        current = current[key] || {};
      });
      current[variableName] = e.target.value;
      return newValue;
    });
    setReset((prev) => !prev);
  };

  const addItemHandler = (path, key) => {
    setWebContetnJson((prev) => {
      // console.log(prev, "prev");
      const newValue = JSON.parse(JSON.stringify(prev));
      let currentpage = templateObj?.pages?.find(
        (page) => page.name == path[0]
      );
      let currentCom = currentpage?.components?.find(
        (com) => com.name == path[1]
      );
      let current = newValue;
      path?.map((key) => {
        current = current[key] || {};
      });
      const intialPlaceholder = {};
      currentCom?.repeatedComponent?.placeholders?.map((placeholder) => {
        intialPlaceholder[placeholder] = "";
      });
      current[key].push(intialPlaceholder);
      // console.log({ newValue }, "newValue");
      return newValue;
    });
    setReset((prev) => !prev);
  };

  const deleteItemHandler = (path, index) => {
    setWebContetnJson((prev) => {
      // console.log(prev, "prev");
      const newValue = JSON.parse(JSON.stringify(prev));
      let current = newValue;
      path?.map((key) => {
        current = current[key] || {};
      });
      current.splice(index, 1);
      // console.log({ newValue }, "newValue");
      return newValue;
    });
    setReset((prev) => !prev);
  };

  // Function to find and get the specific field value
  const getFieldValue = (fieldName) => {
    const searchInObject = (obj) => {
      for (const key in obj) {
        if (key === fieldName) {
          return obj[key];
        }
        if (typeof obj[key] === "object" && obj[key] !== null) {
          const result = searchInObject(obj[key]);
          if (result !== undefined) return result;
        }
      }
      return undefined;
    };
    return searchInObject(webContentJson) || "";
  };

  // Function to update the specific field in the nested object
  // const updateFieldValue = (fieldName, newValue) => {
  //   const updateInObject = (obj) => {
  //     const newObj = { ...obj };
  //     for (const key in newObj) {
  //       if (key === fieldName) {
  //         newObj[key] = newValue;
  //         return newObj;
  //       }
  //       if (typeof newObj[key] === 'object' && newObj[key] !== null) {
  //         const updatedNestedObj = updateInObject(newObj[key]);
  //         if (updatedNestedObj !== newObj[key]) {
  //           newObj[key] = updatedNestedObj;
  //           return newObj;
  //         }
  //       }
  //     }
  //     return newObj;
  //   };

  //   setWebContentJson(updateInObject(webContentJson));
  // };

  // i want to when filterContentJson inside object value update then we want add this value webContentJson inside also want to update
  const tabContents = {
    contents: {
      key: "contents",
      label: "Content",
      icon: <ListCollapse className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <ContentCollapseBar
          contentJSON={webContentJson}
          setContentJSON={setWebContetnJson}
          contentChangeHandler={contentChangeHandler}
          addItemHandler={addItemHandler}
          deleteItemHandler={deleteItemHandler}
          // setContentJSON={setFilterContetnJson}
          saving={saving}
          formData={formData}
          pages={pages}
          pageList={pageList}
          setReset={setReset}
        />
      ),
    },
    media: {
      key: "media",
      label: "Media",
      icon: <Image className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <MediaBar
          formData={formData}
          setFormData={setFormData}
          fileList={fileList}
          setFileList={setFileList}
        />
      ),
    },
  };

  // useEffect(() => {
  //   setContentJSON((prev) => ({
  //     ...prev,
  //     ...formData?.contentJSON,
  //   }));
  // }, [formData?.contentJSON]);

  // Debounced contentJSON for preview updates (500ms delay)
  // const debouncedContentJSON = useDebounce(contentJSON, 500);

  const handleSaveContent = async () => {
    try {
      // Update formData with generated content
      const updatedFormData = {
        ...formData,
        // full_template_content: fullTemplateContent,
        // templateComponentList: templateComponentList,
        // contentJSON: contentJSON || [],
        FileList: fileList || [],
      };
      handleSubmit(updatedFormData);
    } catch (error) {
      console.error("Error generating template structure:", error);
      message.error("Failed to generate template structure. Please try again.");
    }
  };

  const handleExport = () => {
    console.log("export");
  };

  return (
    <DndProvider backend={HTML5Backend}>
      {/* <div className="tw-py-4">
        <Collapse
          size="middle"
          className="tw-bg-white"
          expandIcon={({ isActive }) => (
            <ChevronDown
              className={`tw-w-3 tw-h-3 tw-text-gray-400 tw-transition-transform tw-duration-200 ${
                isActive ? "tw-rotate-180" : ""
              }`}
            />
          )}
          expandIconPosition="end"
          items={[
            {
              key: "branding_details",
              label: (
                <span className="tw-text-lg tw-font-semibold  tw-text-gray-600">
                  Branding Details
                </span>
              ),
              children: (
                <BrandingDetailsCard
                  values={bradingDetails}
                  onChange={handleBrandingChange}
                  predefinedColors={predefinedColors}
                />
              ),
            },
          ]}
        />
      </div> */}
      <Card className="tw-mt-4" styles={{ body: { padding: "16px" } }}>
        <div className="tw-h-screen tw-flex tw-overflow-hidden tw-space-x-2">
          <PageListPreview
            // contentJSON={contentJSON}
            // fileList={fileList}
            // components={components}
            pageList={pageList}
            exporthandler={handleExport}
          />

          {/* Right Side - Content Editor */}
          <div className="tw-h-full tw-w-full tw-flex tw-flex-col tw-bg-white tw-flex-1">
            <TabList
              tabContents={tabContents}
              setPreviewMode={setPreviewMode}
              previewMode={previewMode}
            />
            <div className="tw-flex-1 tw-flex tw-flex-col tw-overflow-hidden">
              {tabContents[previewMode]?.content ? (
                tabContents[previewMode]?.content
              ) : (
                <></>
              )}
            </div>
          </div>
        </div>
      </Card>
    </DndProvider>
  );
};

export default BradingTab;
