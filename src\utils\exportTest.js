// Test file for website export functionality
import { exportWebsite } from './websiteExporter';

// Sample test data
const testExportData = {
  pages: [
    {
      name: "Home",
      type: "static",
      url: "/",
      full_page_content: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Home Page</title>
</head>
<body>
  <h1>Welcome to Our Website</h1>
  <p>This is the home page content.</p>
</body>
</html>`,
      components: [],
      custom_css: "",
      custom_js: ""
    },
    {
      name: "About",
      type: "static", 
      url: "/about",
      full_page_content: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>About Us</title>
</head>
<body>
  <h1>About Us</h1>
  <p>Learn more about our company.</p>
</body>
</html>`,
      components: [],
      custom_css: "",
      custom_js: ""
    },
    {
      name: "Mumbai",
      type: "dynamic",
      url: "/city/mumbai",
      full_page_content: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mumbai - City Guide</title>
</head>
<body>
  <h1>Mumbai City Guide</h1>
  <p>Discover the amazing city of Mumbai.</p>
</body>
</html>`,
      components: [],
      custom_css: "",
      custom_js: ""
    },
    {
      name: "Delhi",
      type: "dynamic",
      url: "/city/delhi", 
      full_page_content: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Delhi - City Guide</title>
</head>
<body>
  <h1>Delhi City Guide</h1>
  <p>Explore the capital city of India.</p>
</body>
</html>`,
      components: [],
      custom_css: "",
      custom_js: ""
    }
  ],
  bradingDetails: {
    primaryColor: "#3B82F6",
    secondaryColor: "#10B981", 
    backgroundColor: "#ffffff",
    textColor: "#1F2937",
    borderColor: "#E5E7EB"
  },
  fileList: {},
  exportMetadata: {
    exportDate: new Date().toISOString(),
    totalPages: 4,
    staticPages: 2,
    dynamicPages: 2
  }
};

// Test function
export const testExport = async () => {
  try {
    console.log('Starting export test...');
    const result = await exportWebsite(testExportData);
    console.log('Export test completed successfully:', result);
    return result;
  } catch (error) {
    console.error('Export test failed:', error);
    throw error;
  }
};

// Make it available globally for testing in browser console
if (typeof window !== 'undefined') {
  window.testExport = testExport;
  window.testExportData = testExportData;
}
