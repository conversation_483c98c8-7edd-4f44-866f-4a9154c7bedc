import J<PERSON>Z<PERSON> from 'jszip';
import { saveAs } from 'file-saver';
import { getAllExportAssets, getAssetReplacementPatterns, createAssetManifest } from './assetDiscovery';

/**
 * Frontend Website Exporter
 * Generates and downloads a complete website ZIP file using only frontend JavaScript
 */

export class WebsiteExporter {
  constructor() {
    this.zip = new JSZip();
    this.assetMap = new Map();
  }

  /**
   * Main export function
   * @param {Object} exportData - Complete export data from BrandingTab
   */
  async exportWebsite(exportData) {
    try {
      console.log('Starting frontend website export...', exportData);

      const { pages, bradingDetails, fileList, exportMetadata } = exportData;

      // Validate input data
      if (!pages || pages.length === 0) {
        throw new Error('No pages found to export. Please ensure you have created pages in the Dynamic Tab.');
      }

      console.log(`Found ${pages.length} pages to export:`, pages.map(p => ({ name: p.name, type: p.type, url: p.url })));

      // Reset ZIP for new export
      this.zip = new JSZip();
      this.assetMap.clear();

      // Create assets folder
      const assetsFolder = this.zip.folder('assets');

      // Process and add user uploaded assets to ZIP
      await this.processAssets(fileList, assetsFolder);

      // Add all project assets (images from public/asset and public/img)
      await this.addProjectAssets(assetsFolder);

      // Generate HTML files for each page
      await this.generatePageFiles(pages, bradingDetails);

      // Generate SEO files
      this.generateSEOFiles(pages);

      // Generate and download ZIP
      const downloadResult = await this.downloadZip(exportMetadata);

      return {
        success: true,
        message: 'Website exported successfully!',
        ...downloadResult,
        assetsIncluded: this.assetMap.size
      };

    } catch (error) {
      console.error('Export failed:', error);
      throw new Error(`Export failed: ${error.message}`);
    }
  }

  /**
   * Process file assets and add them to ZIP
   */
  async processAssets(fileList, assetsFolder) {
    if (!fileList || Object.keys(fileList).length === 0) {
      console.log('No user uploaded assets to process');
      return;
    }

    console.log('Processing user uploaded assets...', Object.keys(fileList));

    for (const [key, fileData] of Object.entries(fileList)) {
      try {
        if (fileData.file) {
          // Handle File objects
          const fileName = fileData.originalName || fileData.filename || `asset_${key}`;
          assetsFolder.file(fileName, fileData.file);
          this.assetMap.set(key, `./assets/${fileName}`);
          console.log(`Added user asset: ${key} -> ${fileName}`);
        } else if (fileData.blobUrl) {
          // Handle blob URLs
          const response = await fetch(fileData.blobUrl);
          const blob = await response.blob();
          const fileName = fileData.originalName || fileData.filename || `asset_${key}`;
          assetsFolder.file(fileName, blob);
          this.assetMap.set(key, `./assets/${fileName}`);
          console.log(`Added blob asset: ${key} -> ${fileName}`);
        } else if (fileData.path && fileData.path.startsWith('/uploads/')) {
          // Handle server paths - try to fetch them
          try {
            const response = await fetch(fileData.path);
            if (response.ok) {
              const blob = await response.blob();
              const fileName = fileData.originalName || fileData.path.split('/').pop();
              assetsFolder.file(fileName, blob);
              this.assetMap.set(key, `./assets/${fileName}`);
              console.log(`Added server asset: ${key} -> ${fileName}`);
            }
          } catch (fetchError) {
            console.warn(`Could not fetch asset ${key} from ${fileData.path}:`, fetchError);
          }
        }
      } catch (error) {
        console.warn(`Failed to process asset ${key}:`, error);
      }
    }
  }

  /**
   * Add all project assets from public/asset folder ONLY
   */
  async addProjectAssets(assetsFolder) {
    console.log('Adding project assets from public/asset folder ONLY...');

    try {
      // Get all available project assets from /asset/ folder only
      const projectAssets = await getAllExportAssets();
      console.log(`Found ${projectAssets.length} assets in /asset/ folder to include`);

      let successCount = 0;
      let failCount = 0;

      for (const asset of projectAssets) {
        try {
          const response = await fetch(asset.path);
          if (response.ok) {
            const blob = await response.blob();

            // Add to assets folder
            assetsFolder.file(asset.filename, blob);

            // Also add to asset map for replacement
            const assetKey = asset.filename.split('.')[0]; // Remove extension for key
            this.assetMap.set(assetKey, `./assets/${asset.filename}`);
            this.assetMap.set(asset.path, `./assets/${asset.filename}`); // Also map full path

            console.log(`✓ Added project asset: ${asset.filename} (${(blob.size / 1024).toFixed(1)}KB)`);
            successCount++;
          } else {
            console.warn(`✗ Could not fetch project asset: ${asset.path}`);
            failCount++;
          }
        } catch (error) {
          console.warn(`✗ Failed to fetch project asset ${asset.path}:`, error);
          failCount++;
        }
      }

      // Create asset manifest
      const manifest = createAssetManifest(projectAssets);
      assetsFolder.file('manifest.json', JSON.stringify(manifest, null, 2));

      console.log(`Asset processing complete: ${successCount} added, ${failCount} failed`);
      console.log('Created asset manifest with details of all included assets');

    } catch (error) {
      console.error('Error adding project assets:', error);
    }
  }



  /**
   * Generate HTML files for all pages
   */
  async generatePageFiles(pages, bradingDetails) {
    console.log(`Generating ${pages.length} page files...`);

    for (const page of pages) {
      try {
        const htmlContent = this.generatePageHTML(page, bradingDetails);
        const filename = this.getPageFilename(page);

        // Create subdirectories if needed
        if (filename.includes('/')) {
          const parts = filename.split('/');
          const dir = parts.slice(0, -1).join('/');
          const file = parts[parts.length - 1];

          const folder = this.zip.folder(dir);
          folder.file(file, htmlContent);
        } else {
          this.zip.file(filename, htmlContent);
        }

        console.log(`Generated: ${filename}`);
      } catch (error) {
        console.error(`Error generating page ${page.name}:`, error);
      }
    }
  }

  /**
   * Generate HTML content for a single page
   */
  generatePageHTML(page, bradingDetails) {
    let htmlContent = page.full_page_content || '';

    // If no content, create a basic page structure
    if (!htmlContent || htmlContent.trim() === '') {
      htmlContent = `<div class="page-content">
        <h1>${page.name || 'Page'}</h1>
        <p>This page was generated automatically. Content will be added here.</p>
      </div>`;
    }

    // Ensure we have valid HTML structure
    if (!htmlContent.includes('<!DOCTYPE html>')) {
      htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${page.name || 'Page'}</title>
  <meta name="description" content="${page.meta_description || ''}">
  <style>
    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
    .page-content { max-width: 1200px; margin: 0 auto; }
    h1 { color: #333; }
  </style>
</head>
<body>
  ${htmlContent}
</body>
</html>`;
    }

    // Replace asset placeholders with actual paths
    this.assetMap.forEach((path, key) => {
      const regex = new RegExp(`\\$\\{${key}\\}`, 'g');
      htmlContent = htmlContent.replace(regex, path);

      // Also handle img- prefixed placeholders
      const imgRegex = new RegExp(`\\$\\{img-${key}\\}`, 'g');
      htmlContent = htmlContent.replace(imgRegex, path);
    });

    // Replace common project asset references
    htmlContent = this.replaceProjectAssetReferences(htmlContent);

    // Apply branding colors and styles
    if (bradingDetails) {
      htmlContent = this.applyBrandingToHTML(htmlContent, bradingDetails);
    }

    return htmlContent;
  }

  /**
   * Replace project asset references in HTML content
   */
  replaceProjectAssetReferences(htmlContent) {
    // Get asset replacement patterns from utility
    const assetReplacements = getAssetReplacementPatterns();

    assetReplacements.forEach(replacement => {
      htmlContent = htmlContent.replace(replacement.from, replacement.to);
    });

    return htmlContent;
  }

  /**
   * Get filename for a page based on its type and URL
   */
  getPageFilename(page) {
    if (page.type === 'static') {
      // Static pages: /pagename -> pagename.html
      const pageName = page.url?.replace(/^\//, '') || page.name;
      return pageName === '' || pageName === 'home' ? 'index.html' : `${pageName}.html`;
    } else if (page.type === 'dynamic') {
      // Dynamic pages: /category/slug -> category/slug.html
      const urlParts = page.url?.split('/').filter(Boolean) || [];
      if (urlParts.length >= 2) {
        const category = urlParts[0];
        const slug = urlParts[1];
        return `${category}/${slug}.html`;
      }
      return `${page.name}.html`;
    }
    return `${page.name || 'page'}.html`;
  }

  /**
   * Apply branding styles to HTML
   */
  applyBrandingToHTML(html, branding) {
    const brandingCSS = `
    <style>
      :root {
        --primary-color: ${branding.primaryColor || '#3B82F6'};
        --secondary-color: ${branding.secondaryColor || '#3B82F6'};
        --background-color: ${branding.backgroundColor || '#ffffff'};
        --text-color: ${branding.textColor || '#000000'};
        --border-color: ${branding.borderColor || '#d9d9d9'};
      }
      
      /* Apply branding colors to common elements */
      .primary-color { color: var(--primary-color) !important; }
      .secondary-color { color: var(--secondary-color) !important; }
      .bg-primary { background-color: var(--primary-color) !important; }
      .bg-secondary { background-color: var(--secondary-color) !important; }
      .border-primary { border-color: var(--primary-color) !important; }
    </style>`;

    // Insert branding CSS before closing head tag
    if (html.includes('</head>')) {
      html = html.replace('</head>', `${brandingCSS}</head>`);
    } else {
      // If no head tag, add it
      html = html.replace('<body>', `<head>${brandingCSS}</head><body>`);
    }

    return html;
  }

  /**
   * Generate SEO files (sitemap.xml, robots.txt)
   */
  generateSEOFiles(pages) {
    // Generate sitemap.xml
    const sitemap = this.generateSitemap(pages);
    this.zip.file('sitemap.xml', sitemap);

    // Generate robots.txt
    const robots = this.generateRobotsTxt();
    this.zip.file('robots.txt', robots);

    console.log('Generated SEO files: sitemap.xml, robots.txt');
  }

  /**
   * Generate sitemap.xml content
   */
  generateSitemap(pages) {
    const baseUrl = 'https://yourwebsite.com'; // This should be configurable

    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    pages.forEach(page => {
      const url = page.url?.startsWith('/') ? page.url : `/${page.url || page.name}`;
      const priority = page.type === 'static' && (url === '/' || url === '/home') ? '1.0' : '0.8';

      sitemap += `
  <url>
    <loc>${baseUrl}${url}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${priority}</priority>
  </url>`;
    });

    sitemap += `
</urlset>`;

    return sitemap;
  }

  /**
   * Generate robots.txt content
   */
  generateRobotsTxt() {
    return `User-agent: *
Allow: /

Sitemap: https://yourwebsite.com/sitemap.xml

# Generated by Dream Builder
# ${new Date().toISOString()}`;
  }

  /**
   * Generate and download the ZIP file
   */
  async downloadZip(exportMetadata) {
    console.log('Generating ZIP file...');

    const content = await this.zip.generateAsync({
      type: 'blob',
      compression: 'DEFLATE',
      compressionOptions: {
        level: 6
      }
    });

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
    const filename = `website-export-${timestamp}.zip`;

    console.log(`Downloading ${filename}...`);
    saveAs(content, filename);

    return {
      filename,
      size: content.size,
      pages: exportMetadata?.totalPages || 0
    };
  }
}

// Export singleton instance
export const websiteExporter = new WebsiteExporter();

// Export utility function
export const exportWebsite = async (exportData) => {
  return await websiteExporter.exportWebsite(exportData);
};
