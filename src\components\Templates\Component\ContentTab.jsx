import { Button, Collapse, Input, Row, Col, message } from "antd";
import {
  ChevronDown,
  ChevronUp,
  Upload as UploadIcon,
  ExpandIcon,
  Minimize2,
  ListCollapse,
  Image,
} from "lucide-react";
import { useState, useEffect, useMemo, useRef, useCallback } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import TemplatePreview from "./TemplatePreview";
import SearchBar from "../../common/SearchBar";
import JsonContentCollapse from "../../common/JsonContentCollapse";
import ContentCollapseBar from "./ContentCollapseBar";
import useDebounce from "../../../hooks/useDebounce";
import TabList from "../../common/TabList";
import MediaBar from "./MediaBar";
import { generateContentJSON } from "../../../utils/contentJSONGenerator";

const { Panel } = Collapse;
const { TextArea } = Input;

// "Hero Section": {
//   $section_title: "Your Adventure Awaits!",
//   $section_content:
//     "Discover and book bus tickets to your favorite destinations with ease. We offer the best prices and a comfortable journey.",
// },
// Popular_Bus_Routes: [
//   {
//     $title: "Coimbatore Buses",
//     $location: "Manali, Jaipur",
//   },
//   {
//     $title: "Mountain Express",
//     $location: "Shimla, Ooty",
//   },
//   {
//     $title: "Coastal Rider",
//     $location: "Goa, Pondicherry",
//   },
// ],

const ContentTab = ({
  formData,
  setFormData,
  onCancel,
  handleSubmit,
  saving,
  pages,
  components = [],
}) => {
  const [contentJSON, setContentJSON] = useState(formData?.contentJSON ?? {});
  const [fileList, setFileList] = useState(formData?.FileList ?? []);

  const [previewMode, setPreviewMode] = useState("contents");
  console.log(fileList, "fileList");
  const tabContents = {
    contents: {
      key: "contents",
      label: "Content",
      icon: <ListCollapse className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <ContentCollapseBar
          contentJSON={contentJSON}
          setContentJSON={setContentJSON}
          saving={saving}
          formData={formData}
          pages={pages}
        />
      ),
    },
    media: {
      key: "media",
      label: "Media",
      icon: <Image className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <MediaBar
          formData={formData}
          setFormData={setFormData}
          fileList={fileList}
          setFileList={setFileList}
        />
      ),
    },
  };
  // Local content JSON state (example data for testing functionality)
  const [placeholder, setPlaceholder] = useState("");

  useEffect(() => {
    setPlaceholder(formData?.placeholders || []);
    if (formData?.pages?.length > 0) {
      setContentJSON((prev) => {
        // Use the global content JSON generator function
        const newContent = generateContentJSON(formData, {
          defaultValue: "",
          includeRepeatedComponents: true,
          mergeWithExisting: true,
          existingContent: prev,
          customProcessors: {
            // Add any custom processing logic here if needed
            repeatedComponent: (item) => {
              // Custom logic for repeated components can be added here
              return item;
            },
          },
        });

        console.log("Generated content using global function:", newContent);
        return newContent;
      });
    }
  }, [formData?.pages]);

  useEffect(() => {
    setContentJSON((prev) => ({
      ...prev,
      ...formData?.contentJSON,
    }));
  }, [formData?.contentJSON]);

  // Debounced contentJSON for preview updates (500ms delay)
  const debouncedContentJSON = useDebounce(contentJSON, 500);

  const handleSaveContent = async () => {
    try {
      // Update formData with generated content
      const updatedFormData = {
        ...formData,
        // full_template_content: fullTemplateContent,
        // templateComponentList: templateComponentList,
        contentJSON: contentJSON || [],
        FileList: fileList || [],
      };
      handleSubmit(updatedFormData);
    } catch (error) {
      console.error("Error generating template structure:", error);
      message.error("Failed to generate template structure. Please try again.");
    }
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="tw-h-screen tw-flex tw-overflow-hidden">
        <TemplatePreview
          isPageLibraryOpen={true}
          //   setIsPageLibraryOpen={setIsPageLibraryOpen}
          isTemplateStructureOpen={true}
          //   setIsTemplateStructureOpen={() => {}} // Disable toggle
          formData={formData}
          setFormData={setFormData}
          pages={pages}
          components={components}
          handleSave={handleSaveContent}
          onCancel={onCancel}
          saving={saving}
          isDrop={false} // Disable drop functionality
          contentJSON={debouncedContentJSON}
          fileList={fileList}
        />

        {/* Right Side - Content Editor */}
        <div className="tw-h-full tw-w-full tw-flex tw-flex-col tw-bg-white tw-flex-1">
          <TabList
            tabContents={tabContents}
            setPreviewMode={setPreviewMode}
            previewMode={previewMode}
          />
          <div className="tw-flex-1 tw-flex tw-flex-col tw-overflow-hidden">
            {tabContents[previewMode]?.content ? (
              tabContents[previewMode]?.content
            ) : (
              <></>
            )}
          </div>
        </div>
      </div>
    </DndProvider>
  );
};

export default ContentTab;
