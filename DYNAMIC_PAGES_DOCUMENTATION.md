# Dynamic Pages Feature Documentation

## Overview

The Dynamic Pages feature provides a comprehensive interface for managing dynamic website content with environment-based layout switching. This feature allows users to configure sitemap navigation, manage sections, and export/import configurations in JSON format.

## Features

### 1. Environment-Based Layout Switching
- **Standard Layout**: Traditional layout with sidebar navigation
- **Feature Layout**: Full-width layout without sidebar for focused content management
- Controlled via `VITE_LAYOUT_MODE` environment variable

### 2. Dynamic Pages Interface
- **Tab-based Navigation**: City, Service, Page 2, Page 3 tabs
- **Configuration Management**: Sitemap labels, navigation types, column counts
- **Section Management**: Add/remove sections with dynamic field management
- **Import/Export**: JSON-based configuration backup and restore

### 3. Responsive Design
- Mobile-friendly interface
- Ant Design components with Tailwind CSS styling
- Consistent design patterns matching the project theme

## Environment Configuration

### Layout Mode Settings

```env
# .env file
VITE_LAYOUT_MODE=feature  # Use 'feature' for full-width layout
VITE_LAYOUT_MODE=default  # Use 'default' for standard sidebar layout
```

### Available Layout Modes

1. **Feature Layout** (`VITE_LAYOUT_MODE=feature`)
   - Full-width interface
   - No sidebar navigation
   - Optimized for content management workflows
   - Perfect for the Dynamic Pages interface

2. **Standard Layout** (`VITE_LAYOUT_MODE=default`)
   - Traditional layout with sidebar
   - Standard navigation menu
   - Consistent with existing application design

## Usage Instructions

### Accessing Dynamic Pages

1. Navigate to `/dynamic-pages` in the application
2. The interface will automatically use the layout specified in environment variables
3. Use the sidebar navigation (in standard mode) or direct URL access (in feature mode)

### Interface Components

#### Tab Navigation
- **City**: Configure city-based dynamic content
- **Service**: Manage service-related pages
- **Page 2**: Additional page configuration
- **Page 3**: Extended page management

#### Configuration Fields
- **Sitemap Label**: Enter navigation label for sitemap
- **Navigation Type**: Select from Single Column, Multi Columns, Dropdown, Mega Menu
- **Column Count**: Choose 1-4 columns for multi-column layouts

#### Section Management
- **Add Section**: Create new content sections
- **Section Label**: Name your content sections
- **Section Items**: Add label/slug pairs for each section
- **Add Field**: Add new label/slug pairs to sections
- **Remove**: Delete sections or fields (minimum one required)

#### Import/Export Functions
- **Import From JSON**: Upload previously exported configurations
- **Export**: Download current configuration as JSON file

### JSON Structure

```json
{
  "formData": {
    "sitemapLabel": "Main Navigation",
    "navigationType": "multi",
    "columnCount": "2"
  },
  "sections": [
    {
      "id": 1,
      "label": "Cities",
      "items": [
        {
          "id": 1,
          "label": "New York",
          "slug": "new-york"
        },
        {
          "id": 2,
          "label": "Los Angeles",
          "slug": "los-angeles"
        }
      ]
    }
  ],
  "exportDate": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0"
}
```

## Technical Implementation

### File Structure

```
src/
├── components/
│   ├── Layout/
│   │   ├── FeatureLayout.jsx     # Full-width layout component
│   │   └── Layout.jsx            # Standard layout component
│   └── Pages/
│       └── DynamicPages.jsx      # Main dynamic pages interface
├── util/
│   └── Route.jsx                 # Updated routing with layout logic
└── index.css                     # Custom styling for dynamic pages tabs
```

### Key Components

1. **DynamicPages.jsx**
   - Main interface component
   - Tab management and form handling
   - Section and field management
   - Import/export functionality

2. **FeatureLayout.jsx**
   - Full-width layout without sidebar
   - Optional header for title/subtitle
   - Optimized for content management

3. **Route.jsx**
   - Environment-based layout selection
   - Dynamic route configuration
   - Layout component switching logic

### Environment Integration

The system automatically detects the `VITE_LAYOUT_MODE` environment variable and switches between layouts:

```javascript
// Get layout mode from environment
const getLayoutComponent = () => {
  const layoutMode = import.meta.env.VITE_LAYOUT_MODE;
  return layoutMode === "feature" ? FeatureLayout : Layout;
};
```

## Development Guidelines

### Adding New Tabs
1. Update the `tabItems` array in `DynamicPages.jsx`
2. Add corresponding form fields if needed
3. Update export/import logic for new data structures

### Customizing Layouts
1. Modify `FeatureLayout.jsx` for full-width layout changes
2. Update `Layout.jsx` for standard layout modifications
3. Ensure responsive design principles are maintained

### Styling Customizations
- Tab styling is controlled via CSS classes in `index.css`
- Use Tailwind CSS utilities for consistent styling
- Follow Ant Design theme configuration

## Testing

### Layout Switching Test
1. Set `VITE_LAYOUT_MODE=feature` in `.env`
2. Restart development server
3. Navigate to `/dynamic-pages`
4. Verify full-width layout without sidebar
5. Change to `VITE_LAYOUT_MODE=default`
6. Restart server and verify sidebar layout

### Functionality Testing
1. Test tab navigation between City, Service, Page 2, Page 3
2. Verify form field updates and validation
3. Test section and field add/remove operations
4. Verify import/export JSON functionality
5. Test responsive design on different screen sizes

## Troubleshooting

### Common Issues

1. **Layout not switching**: Ensure server restart after changing environment variables
2. **Import errors**: Verify JSON structure matches expected format
3. **Styling issues**: Check Tailwind CSS classes and Ant Design theme configuration
4. **Navigation problems**: Verify route configuration in `Route.jsx`

### Environment Variables Not Working
- Ensure variables are prefixed with `VITE_`
- Restart development server after changes
- Check `.env` file is in project root
- Verify no syntax errors in `.env` file

## Future Enhancements

- Add more navigation types
- Implement drag-and-drop section reordering
- Add validation for form fields
- Implement auto-save functionality
- Add preview mode for configurations
- Support for nested sections
- Integration with backend API for persistence
