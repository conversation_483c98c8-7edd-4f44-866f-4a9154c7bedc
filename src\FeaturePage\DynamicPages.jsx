import React, { useState, useRef, useEffect } from "react";
import { Select, Typography } from "antd";
import { Setting<PERSON>, Eye, ChevronDown } from "lucide-react";
import TabList from "../components/common/TabList";
import DynamicTab from "./components/DynamicTab";
import BradingTab from "./components/BradingTab";
import useStorage from "../hooks/use-storage";
import { CONSTANTS } from "../util/constant/CONSTANTS";
import { dynamicContentForm } from "./components/function";

const { Title, Text } = Typography;
import template from "../utils/template.json";

const DynamicPages = () => {
  const [previewMode, setPreviewMode] = useState("dynamic_pages");
  const [templateObj, setTemplateObj] = useState(null);
  const [dynamicpages, setdynamicPages] = useState({});
  const [components, setComponents] = useState([]);
  const [saving, setSaving] = useState(false);
  const api = useStorage();
  // console.log(dynamicpages, templateObj, "templatePage");

  useEffect(() => {
    setTemplateObj(template);
  }, []);

  useEffect(() => {
    // Fetch components for repeated component functionality
    api.sendRequest(CONSTANTS.API.components.get, (res) => {
      // console.log("Components fetched for template editor:", res);
      setComponents(res);
    });
  }, []);

  const tabList = {
    dynamic_pages: {
      key: "dynamic_pages",
      label: "Dynamic Pages",
      icon: <Settings className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <div className=" tw-pt-4">
          <DynamicTab
            templatePage={templateObj}
            // setTemplatePage={setTemplatePage}
            pages={dynamicpages}
            setPages={setdynamicPages}
            components={components}
          />
        </div>
      ),
    },
    branding: {
      key: "branding",
      label: "Branding & Content Preview",
      icon: <Eye className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <div className=" tw-pt-4">
          <BradingTab
            templateObj={templateObj}
            setTemplateObj={setTemplateObj}
            components={components}
            saving={saving}
            dynamicpages={dynamicContentForm}
          />
        </div>
      ),
    },
  };

  return (
    <div className="tw-min-h-screen tw-bg-gray-50 tw-p-6">
      <div className="tw-max-w-7xl tw-mx-auto">
        {/* Header */}
        <div className="tw-mb-0">
          <Title level={2} className="!tw-mb-2 !tw-text-gray-900">
            Dream Builder v.0.1
          </Title>
          <Text type="secondary" className="tw-text-base">
            Configure your website settings and content
          </Text>
        </div>

        <div className="tw-p-6 !tw-px-0  tw-rounded-xl ">
          {/* Tab Navigation using project's TabList */}
          <TabList
            tabContents={tabList}
            setPreviewMode={setPreviewMode}
            previewMode={previewMode}
          />
        </div>
      </div>
    </div>
  );
};

export default DynamicPages;
