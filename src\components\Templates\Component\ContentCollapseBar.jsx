import React, { useCallback, useMemo, useRef, useState } from "react";
import SearchBar from "../../common/SearchBar";
import { Button, Collapse, Input, message } from "antd";
import JsonContentCollapse from "../../common/JsonContentCollapse";
import { addAtPath, deleteAtPath, updateAtPath } from "../../../util/functions";
import { deepMerge } from "../../../FeaturePage/components/function";
// import { buildItemList } from "../../../util/functions";

const { Panel } = Collapse;
const { TextArea } = Input;

const ContentCollapseBar = ({
  contentJSON,
  setContentJSON,
  saving,
  contentChangeHandler,
  addItemHandler,
  deleteItemHandler,
  setReset,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedAll, setExpandedAll] = useState(false);
  const [importing, setImporting] = useState(false);
  const [loading, setLoading] = useState(false);
  const fileInputRef = useRef(null);

  const handleAddItem = useCallback((path, key) => {
    try {
      // setContentJSON((prev) => addAtPath(prev, path));
      addItemHandler(path, key);
      message.success("Item added");
    } catch (e) {
      console.error("Error in handleAddItem:", e);
      message.error("Failed to add item");
    }
  }, []);

  const handleSearch = useCallback((value) => {
    // console.log(value);
    setSearchTerm(value);
  }, []);

  const handleDelete = useCallback((path, fieldKey = null) => {
    try {
      setContentJSON((prev) => deleteAtPath(prev, path, fieldKey));
      message.success("Item deleted successfully");
    } catch (e) {
      console.error("Error in handleDelete:", e);
      message.error("Failed to delete item");
    }
  }, []);

  const buildItemList = (data, expandedAll, path = []) => {
    if (Array.isArray(data)) {
      return data.map((item, index) => ({
        key: [...path, index].join("_"),
        label: `Position ${index + 1}`,
        isDelete: true,
        onDelete: () => deleteItemHandler([...path], index),
        children: (
          <>
            {/* recurse deeper only if object/array */}
            {typeof item === "object" && item !== null ? (
              <JsonContentCollapse
                expanded={expandedAll}
                itemList={buildItemList(item, expandedAll, [...path, index])}
                // onDeleteItem={(it) => handleDelete([...path, index], it.key)}
              />
            ) : (
              <TextArea
                rows={2}
                value={item}
                onChange={(e) => {
                  console.log(path, index, e.target.value, "e.target.value");
                  setContentJSON((prev) =>
                    updateAtPath(prev, [...path, index], () => e.target.value)
                  );
                }}
              />
            )}

            {/* <div className="tw-mt-2">
              <Button type="dashed" onClick={() => handleAddItem(path)}>
                + Add Item
              </Button>
              <Button
                danger
                size="small"
                style={{ marginLeft: "6px" }}
                onClick={() => handleDelete(path, null)}
              >
                Delete This
              </Button>
            </div> */}
          </>
        ),
      }));
    } else if (typeof data === "object" && data !== null) {
      return Object.keys(data).map((key) => ({
        key: [...path, key].join("_"),
        label: key,

        extra: Array.isArray(data[key]),
        // onDelete: () => handleDelete(path, key),
        children:
          typeof data[key] === "object" && data[key] !== null ? (
            <>
              <JsonContentCollapse
                expanded={expandedAll}
                itemList={buildItemList(data[key], expandedAll, [...path, key])}
                // onDeleteItem={(it) => handleDelete([...path, key], it.key)}
              />
              {Array.isArray(data[key]) && (
                <div className="tw-mt-2 tw-w-full tw-flex tw-justify-center tw-items-center">
                  <Button
                    type="dashed"
                    className="tw-w-full"
                    onClick={() => handleAddItem([...path], key)}
                  >
                    + Add Item
                  </Button>
                </div>
              )}
            </>
          ) : (
            <TextArea
              rows={2}
              value={data[key]}
              onChange={
                (e) =>
                  contentChangeHandler({
                    variableName: key,
                    value: e.target.value,

                    e,
                    path,
                  })
                // setContentJSON((prev) =>
                //   updateAtPath(prev, [...path, key], () => e.target.value)
                // )
              }
            />
          ),
      }));
    } else {
      // ✅ Primitive leaf node → directly render input, no nested collapse
      return [
        {
          key: path.join("_"),
          label: path[path.length - 1],
          children: (
            <TextArea
              rows={2}
              value={data}
              onChange={(e) =>
                setContentJSON((prev) =>
                  updateAtPath(prev, path, () => e.target.value)
                )
              }
            />
          ),
        },
      ];
    }
  };

  const toggleExpandAll = () => {
    setLoading(true);
    setTimeout(() => {
      setExpandedAll((prev) => !prev);
      setLoading(false);
    }, 0);
  };

  const handleImportJSON = () => {
    if (fileInputRef.current) fileInputRef.current.click();
  };

  const onFileChange = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setImporting(true);
    try {
      const text = await file.text();
      const parsed = JSON.parse(text);
      if (!parsed || typeof parsed !== "object" || Array.isArray(parsed)) {
        throw new Error("Invalid JSON structure. Expected an object at root.");
      }
      console.log(parsed, "parsed");
      setContentJSON((pr) => {
        return deepMerge(pr, parsed);
      });
      message.success("JSON imported successfully");
    } catch (err) {
      console.error("Import JSON error:", err);
      message.error("Failed to import JSON. Please check the file.");
    } finally {
      setImporting(false);
      setReset((prev) => !prev);
      // Reset input value to re-trigger change event if same file is selected again
      if (fileInputRef.current) fileInputRef.current.value = "";
    }
  };

  const filteredContent = useMemo(() => {
    if (!contentJSON) return {};
    if (!searchTerm) return contentJSON;

    const lowercasedTerm = searchTerm.toLowerCase();
    return Object.keys(contentJSON)
      .filter((key) => key.toLowerCase().includes(lowercasedTerm))
      .reduce((obj, key) => {
        obj[key] = contentJSON[key];
        return obj;
      }, {});
  }, [contentJSON, searchTerm]);

  const itemList = useMemo(() => {
    const dataToDisplay = searchTerm ? filteredContent : contentJSON;
    if (!dataToDisplay) return [];
    return Object.keys(dataToDisplay).map((key) => ({
      key,
      label: key,
      // extra: Array.isArray(dataToDisplay[key]),
      children: (
        <>
          <JsonContentCollapse
            expanded={expandedAll}
            itemList={buildItemList(dataToDisplay[key], expandedAll, [key])}
            // onDeleteItem={(it) => handleDelete([key], it.key)}
          />
        </>
      ),
    }));
  }, [
    contentJSON,
    searchTerm,
    filteredContent,
    expandedAll,
    handleDelete,
    handleAddItem,
  ]);

  // const itemList = useMemo(
  //   () => buildItemList(filteredContent, expandedAll),
  //   [filteredContent, expandedAll]
  // );

  return (
    <div className="tw-h-full tw-flex tw-flex-col tw-overflow-hidden">
      {/* Header Section - Fixed */}
      <div className="tw-flex-shrink-0 tw-p-4 tw-border-b tw-border-gray-200">
        <div className="tw-mb-4">
          <SearchBar handleSearch={(e) => handleSearch(e)} />
        </div>
        <div className="tw-flex tw-space-x-4 tw-w-full tw-justify-between tw-items-center">
          <Button
            type="primary"
            size="large"
            onClick={handleImportJSON}
            disabled={saving || importing}
            className="tw-px-6 tw-w-full tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
          >
            Import JSON
          </Button>

          <Button
            size="large"
            onClick={toggleExpandAll}
            loading={loading}
            disabled={loading}
            className="tw-flex tw-w-full tw-items-center tw-text-black tw-border-blue-200 hover:tw-bg-blue-50"
          >
            {expandedAll ? "Collapse All" : "Expand All"}
          </Button>
        </div>
        {/* Hidden file input for JSON import */}
        <input
          type="file"
          accept="application/json,.json"
          ref={fileInputRef}
          onChange={onFileChange}
          style={{ display: "none" }}
        />
      </div>

      {/* Scrollable Content Section */}
      <div className="tw-flex-1 tw-overflow-y-auto tw-p-4">
        {itemList?.length ? (
          <JsonContentCollapse itemList={itemList} expanded={expandedAll} />
        ) : (
          <div className="tw-text-center tw-py-8">
            <p className="tw-text-gray-500 tw-mb-2">No content fields found</p>
            <p className="tw-text-sm tw-text-gray-400">
              Add content fields to see them here
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentCollapseBar;
