/**
 * useMediaManager Hook
 * Handles media file operations including upload, delete, and state management
 */

import { useState, useCallback, useEffect } from 'react';
import { message } from 'antd';
import Services from '../util/API/service';
import { CONSTANTS } from '../util/constant/CONSTANTS';

const useMediaManager = () => {
  const [mediaFiles, setMediaFiles] = useState({});
  const [uploading, setUploading] = useState(false);
  const [loading, setLoading] = useState(false);

  // Initialize with sample data (remove when API is ready)
  // useEffect(() => {
  //   const sampleData = {
  //     "hero_content_image1": {
  //       id: 1,
  //       file: null, // Will be populated when real files are uploaded
  //       filename: "hero_content_image1.png",
  //       originalName: "hero_content_image1.png",
  //       path: "/uploads/hero_content_image1.png",
  //       size: 245760,
  //       uploadDate: new Date().toISOString(),
  //       type: "image/png"
  //     },
  //     "content_image1": {
  //       id: 2,
  //       file: null,
  //       filename: "content_image1.png",
  //       originalName: "content_image1.png",
  //       path: "/uploads/content_image1.png",
  //       size: 189440,
  //       uploadDate: new Date().toISOString(),
  //       type: "image/png"
  //     },
  //     "content_image2": {
  //       id: 3,
  //       file: null,
  //       filename: "content_image2.png",
  //       originalName: "content_image2.png",
  //       path: "/uploads/content_image2.png",
  //       size: 156672,
  //       uploadDate: new Date().toISOString(),
  //       type: "image/png"
  //     },
  //     "content_image3": {
  //       id: 4,
  //       file: null,
  //       filename: "content_image3.png",
  //       originalName: "content_image3.png",
  //       path: "/uploads/content_image3.png",
  //       size: 203520,
  //       uploadDate: new Date().toISOString(),
  //       type: "image/png"
  //     }
  //   };
  //   setMediaFiles(sampleData);
  // }, []);

  // Upload file to server
  const uploadFile = useCallback(async (file) => {
    setUploading(true);

    try {
      // Create blob URL for immediate display
      const blobUrl = URL.createObjectURL(file);

      // Extract image key from filename (without extension)
      const imageKey = file.name.replace(/\.[^/.]+$/, "");

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('image', file);

      // For now, simulate upload. Uncomment below for real API call
      /*
      const response = await Services.post(CONSTANTS.API.upload.image.endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const uploadedFile = {
        id: Date.now(),
        file: file, // Store the actual file object
        filename: response.data.filename,
        originalName: response.data.originalName,
        path: response.data.path,
        blobUrl: blobUrl, // For immediate display
        size: response.data.size,
        uploadDate: new Date().toISOString(),
        type: file.type
      };
      */

      // Simulate upload response with new structure
      const uploadedFile = {
        id: Date.now(),
        file: file, // Store the actual file object for direct use
        filename: `${Date.now()}-${file.name}`,
        originalName: file.name,
        path: `/uploads/${Date.now()}-${file.name}`,
        blobUrl: blobUrl, // For immediate display in preview
        size: file.size,
        uploadDate: new Date().toISOString(),
        type: file.type
      };

      // Add to state using image key as the object key
      setMediaFiles(prev => ({
        ...prev,
        [imageKey]: uploadedFile
      }));

      message.success('File uploaded successfully');

      return uploadedFile;

    } catch (error) {
      console.error('Upload error:', error);
      message.error('Failed to upload file');
      throw error;
    } finally {
      setUploading(false);
    }
  }, []);

  // Delete file
  const deleteFile = useCallback(async (fileKey) => {
    try {
      // Clean up blob URL to prevent memory leaks
      const fileToDelete = mediaFiles[fileKey];
      if (fileToDelete?.blobUrl) {
        URL.revokeObjectURL(fileToDelete.blobUrl);
      }

      // For now, just remove from state. Add API call when ready
      /*
      await Services.delete(`/api/media/${fileKey}`);
      */

      setMediaFiles(prev => {
        const newFiles = { ...prev };
        delete newFiles[fileKey];
        return newFiles;
      });

      message.success('File deleted successfully');

    } catch (error) {
      console.error('Delete error:', error);
      message.error('Failed to delete file');
      throw error;
    }
  }, [mediaFiles]);

  // Get file by key
  const getFileByKey = useCallback((fileKey) => {
    return mediaFiles[fileKey];
  }, [mediaFiles]);

  // Get files by type
  const getFilesByType = useCallback((type) => {
    return Object.values(mediaFiles).filter(file => file.type?.startsWith(type));
  }, [mediaFiles]);

  // Search files
  const searchFiles = useCallback((searchTerm) => {
    if (!searchTerm.trim()) return Object.values(mediaFiles);

    return Object.values(mediaFiles).filter(file =>
      file.originalName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      file.filename.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [mediaFiles]);

  // Format file size
  const formatFileSize = useCallback((bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  // Get file URL for display (prioritize blob URL for immediate display)
  const getFileUrl = useCallback((file) => {
    // Use blob URL if available (for immediate display), otherwise use path
    if (file.blobUrl) {
      return file.blobUrl;
    }
    // In production, this would be the full URL to the file
    return `${import.meta.env.VITE_API_URL || 'http://localhost:3543'}${file.path}`;
  }, []);

  // Get all files as array (for compatibility)
  const getFilesArray = useCallback(() => {
    return Object.values(mediaFiles);
  }, [mediaFiles]);

  return {
    mediaFiles,
    uploading,
    loading,
    uploadFile,
    deleteFile,
    getFileByKey,
    getFilesByType,
    searchFiles,
    formatFileSize,
    getFileUrl,
    getFilesArray
  };
};

export default useMediaManager;
