# Website Export Functionality Guide

## Overview
The website export functionality allows you to export your complete website project as a downloadable ZIP file containing all HTML pages, assets, and necessary files for deployment.

## How It Works

### 1. Export Process Flow
```
BrandingTab → Export Button → Frontend Handler → Backend API → ZIP Generation → Download
```

### 2. Data Structure
The export system processes two types of pages:

#### Static Pages
- **Type**: `"static"`
- **URL Structure**: `/pagename`
- **File Output**: `pagename.html`
- **Example**: `/about` → `about.html`

#### Dynamic Pages
- **Type**: `"dynamic"`
- **URL Structure**: `/category/slug`
- **File Output**: `category/slug.html`
- **Example**: `/city/mumbai` → `city/mumbai.html`

### 3. Export Data Structure
```javascript
{
  pages: [
    {
      name: "Home",
      type: "static",
      url: "/",
      components: [...],
      full_page_content: "<!DOCTYPE html>...",
      custom_css: "...",
      custom_js: "..."
    },
    {
      name: "Mumbai",
      type: "dynamic", 
      url: "/city/mumbai",
      components: [...],
      full_page_content: "<!DOCTYPE html>...",
      custom_css: "...",
      custom_js: "..."
    }
  ],
  templateObj: {...},
  bradingDetails: {
    primaryColor: "#3B82F6",
    secondaryColor: "#3B82F6",
    backgroundColor: "#ffffff",
    textColor: "#000000",
    borderColor: "#d9d9d9"
  },
  fileList: {...},
  dynamicpages: {...}
}
```

## Implementation Details

### Frontend (BrandingTab.jsx)
```javascript
const handleExport = async () => {
  // 1. Prepare export data
  const exportData = {
    pages: pageList,
    templateObj: templateObj,
    bradingDetails: bradingDetails,
    webContentJson: webContentJson,
    fileList: fileList,
    dynamicpages: dynamicpages,
    exportMetadata: {...}
  };

  // 2. Create FormData with assets
  const formData = new FormData();
  formData.append('exportData', JSON.stringify(exportData));
  
  // 3. Add file assets
  Object.entries(fileList).forEach(([key, file]) => {
    if (file.file) {
      formData.append(`assets_${key}`, file.file, file.originalName);
    }
  });

  // 4. Send to backend
  const response = await fetch('/api/export/website', {
    method: 'POST',
    body: formData,
  });

  // 5. Download ZIP file
  const result = await response.json();
  // Download logic...
};
```

### Backend (websiteExport.js)
```javascript
router.post("/website", upload.any(), async (req, res) => {
  // 1. Parse export data
  const exportData = JSON.parse(req.body.exportData);
  
  // 2. Create temporary directories
  const websiteDir = path.join(tempDir, "website");
  const assetsDir = path.join(websiteDir, "assets");
  
  // 3. Process uploaded assets
  const assetMap = {};
  req.files.forEach(file => {
    // Copy assets to assets directory
  });
  
  // 4. Generate HTML files
  for (const page of pages) {
    const htmlContent = generatePageHTML(page, bradingDetails, assetMap);
    const filename = getPageFilename(page);
    fs.writeFileSync(path.join(websiteDir, filename), htmlContent);
  }
  
  // 5. Generate sitemap.xml and robots.txt
  // 6. Create ZIP file
  // 7. Send download response
});
```

## File Structure Output

### Generated ZIP Structure
```
website-export-[timestamp].zip
├── index.html                 (Home page)
├── about.html                 (Static page)
├── contact.html               (Static page)
├── city/
│   ├── mumbai.html           (Dynamic page)
│   ├── delhi.html            (Dynamic page)
│   └── bangalore.html        (Dynamic page)
├── service/
│   ├── web-development.html  (Dynamic page)
│   ├── mobile-app.html       (Dynamic page)
│   └── ui-ux-design.html     (Dynamic page)
├── assets/
│   ├── logo.png
│   ├── hero-image.jpg
│   └── background.svg
├── sitemap.xml               (SEO sitemap)
└── robots.txt                (Search engine instructions)
```

### Page Filename Logic
```javascript
function getPageFilename(page) {
  if (page.type === "static") {
    // Static: /about → about.html
    // Static: / or /home → index.html
    const pageName = page.url?.replace(/^\//, "") || page.name;
    return pageName === "" || pageName === "home" ? "index.html" : `${pageName}.html`;
  } else if (page.type === "dynamic") {
    // Dynamic: /city/mumbai → city/mumbai.html
    const urlParts = page.url?.split("/").filter(Boolean) || [];
    if (urlParts.length >= 2) {
      const category = urlParts[0];
      const slug = urlParts[1];
      return `${category}/${slug}.html`;
    }
  }
  return `${page.name || "page"}.html`;
}
```

## Features

### 1. Asset Management
- Automatically processes uploaded images and files
- Creates proper asset directory structure
- Updates HTML references to use relative paths
- Supports multiple file formats (images, documents, etc.)

### 2. Branding Integration
- Applies branding colors as CSS custom properties
- Injects branding styles into each page
- Maintains consistent design across all pages

### 3. SEO Optimization
- Generates sitemap.xml with all page URLs
- Creates robots.txt for search engine guidance
- Proper URL structure for both static and dynamic pages
- Meta tags and page titles preserved

### 4. Content Replacement
- Processes all placeholder variables (${variable})
- Handles dynamic content based on slug data
- Replaces image placeholders with actual asset paths
- Maintains component structure and styling

## Usage Instructions

### 1. Prepare Your Website
- Complete all page configurations in Dynamic Tab
- Upload all required assets (images, files)
- Configure branding details
- Preview all pages to ensure content is correct

### 2. Export Process
- Navigate to Branding & Content Preview tab
- Review the page list in the left sidebar
- Click "Export Website" button
- Wait for processing (may take a few moments for large sites)
- Download will start automatically when ready

### 3. Deployment
- Extract the ZIP file to your web server
- Upload all files to your hosting provider
- Ensure proper file permissions
- Test all pages and links
- Submit sitemap.xml to search engines

## Troubleshooting

### Common Issues
1. **Export Button Disabled**: Ensure you have at least one page configured
2. **Missing Assets**: Check that all images are properly uploaded
3. **Broken Links**: Verify URL structures in dynamic page configuration
4. **Large File Size**: Optimize images before upload

### Error Messages
- "Export failed: No download URL received" - Server processing error
- "Failed to export website" - General server error
- "No valid pages found" - Check page configuration

## Technical Requirements

### Frontend Dependencies
- React 18+
- Ant Design 5+
- Modern browser with FormData support

### Backend Dependencies
- Node.js 16+
- Express.js
- Multer for file uploads
- Archiver for ZIP creation
- File system access

## Performance Considerations

### Optimization Tips
1. **Image Optimization**: Compress images before upload
2. **Asset Management**: Remove unused assets
3. **Page Count**: Large sites (100+ pages) may take longer to process
4. **File Size Limits**: Current limit is 50MB per file upload

### Processing Time
- Small sites (1-10 pages): 5-15 seconds
- Medium sites (10-50 pages): 15-60 seconds  
- Large sites (50+ pages): 1-5 minutes

The export functionality provides a complete, production-ready website that can be deployed to any web hosting service.
