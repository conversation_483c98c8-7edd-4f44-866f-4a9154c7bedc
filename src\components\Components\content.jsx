import {
  Braces,
  Code,
  Eye,
  Monitor,
  Palette,
  Smartphone,
  Tablet,
} from "lucide-react";
import { generateTailwindCSS } from "../../utils/generatePreviewCSS";

export const htmlPlaceholder = `Enter your HTML content here...

Example with placeholders:
<div class='hero-section tw-bg-gradient-to-r tw-from-blue-500 tw-to-purple-600'>
  <div class='tw-container tw-mx-auto tw-px-4 tw-py-16'>
    <h1 class='tw-text-4xl tw-font-bold tw-text-white tw-mb-4'>
      \${title}
    </h1>
    <p class='tw-text-xl tw-text-white tw-opacity-90 tw-mb-8'>
      \${content}
    </p>
    <img src='\${image_url}' alt='\${alt_text}' class='tw-rounded-lg tw-shadow-lg' />
    <button class='tw-bg-white tw-text-blue-600 tw-px-6 tw-py-3 tw-rounded-lg tw-font-semibold'>
      \${button_text}
    </button>
  </div>
</div>`;

export const cssPlaceholder = `Enter your CSS styles here...

Example:
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 4rem 0;
  text-align: center;
}

.hero-title {
  font-size: 3rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
}`;

export const jsPlaceholder = `Enter your JavaScript code here...

Example:
// Add interactive functionality
document.addEventListener('DOMContentLoaded', function() {
  const button = document.querySelector('.hero-button');

  button.addEventListener('click', function() {
    alert('Button clicked!');
  });

  // Smooth scroll animation
  button.addEventListener('click', function(e) {
    e.preventDefault();
    document.querySelector('#next-section').scrollIntoView({
      behavior: 'smooth'
    });
  });
});`;

export const tabList = [
  {
    key: "code",
    tab: "Code",
    icon: <Code className="tw-w-4 tw-h-4 tw-mr-2" />,
  },
  {
    key: "css",
    tab: "CSS",
    icon: <Palette className="tw-w-4 tw-h-4 tw-mr-2" />,
  },
  {
    key: "javascript",
    tab: "JavaScript",
    icon: <Braces className="tw-w-4 tw-h-4 tw-mr-2" />,
  },
  {
    key: "preview",
    tab: "Preview",
    icon: <Eye className="tw-w-4 tw-h-4 tw-mr-2" />,
  },
];

export const getSampleData = (placeholder) => {
  const lowerPlaceholder = placeholder.toLowerCase();

  if (
    lowerPlaceholder.includes("title") ||
    lowerPlaceholder.includes("heading")
  ) {
    return "Sample Title";
  } else if (lowerPlaceholder.includes("subtitle")) {
    return "Sample Subtitle";
  } else if (
    lowerPlaceholder.includes("content") ||
    lowerPlaceholder.includes("text")
  ) {
    return "This is sample content text that shows how your component will look with real data.";
  } else if (
    lowerPlaceholder.includes("image") ||
    lowerPlaceholder.includes("img")
  ) {
    return "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=250&fit=crop";
  } else if (
    lowerPlaceholder.includes("button") ||
    lowerPlaceholder.includes("cta")
  ) {
    return "Click Here";
  } else if (lowerPlaceholder.includes("name")) {
    return "John Doe";
  } else if (lowerPlaceholder.includes("email")) {
    return "<EMAIL>";
  } else {
    return `[${placeholder}]`;
  }
};

// Helper function to replace placeholders in HTML content
const replacePlaceholders = (html, content, fileList = null) => {
  if (!html) return html;

  let processedHTML = html;

  if (content && typeof content === "object") {
    // Replace placeholders that have values
    Object.keys(content).forEach((key) => {
      if (key?.includes("img-") && fileList) {
        const imageKey = key?.replace("img-", "");
        const file = fileList?.[content?.[key]];

        if (file) {
          const value = file.blobUrl || file.path;
          const regex = new RegExp(`\\$\\{${key}\\}`, "g");
          processedHTML = processedHTML.replace(regex, value);
        }
      }
      const value = content[key];
      if (!value && value == "") return;
      if (typeof value === "string" || typeof value === "number") {
        const regex = new RegExp(`\\$\\{${key}\\}`, "g");
        processedHTML = processedHTML.replace(regex, value);
        // console.log(`Replaced ${key} with:`, value);
      }
    });
  }

  // For better UX, we can optionally style empty placeholders
  // Replace any remaining ${key} with styled placeholders for better visibility
  // processedHTML = processedHTML.replace(
  //   /\$\{([^}]+)\}/g,
  //   '<span style="background-color: #fef3c7; color: #92400e; padding: 2px 6px; border-radius: 4px; font-size: 0.875em; border: 1px dashed #f59e0b;">${$1}</span>'
  // );

  return processedHTML;
};

// Helper function to get component-specific content from hierarchical structure
const getComponentContent = (
  contentJSON,
  title,
  componentName,
  fallbackContent = null
) => {
  if (!contentJSON) return fallbackContent;

  // Try hierarchical structure first: contentJSON[title][componentName]
  const hierarchicalContent = contentJSON?.[title]?.[componentName];
  if (hierarchicalContent) {
    // console.log(
    //   `Found hierarchical content for ${title} -> ${componentName}:`,
    //   hierarchicalContent
    // );
    return hierarchicalContent;
  }

  // Try direct component name access: contentJSON[componentName]
  const directContent = contentJSON?.[componentName];
  if (directContent) {
    // console.log(`Found direct content for ${componentName}:`, directContent);
    return directContent;
  }

  // Fallback to provided content or global contentJSON
  // console.log(
  //   `Using fallback content for ${componentName}:`,
  //   fallbackContent || contentJSON
  // );
  return fallbackContent || contentJSON;
};

// Helper function to find nested content by key path (for repeated components)
const findNestedContent = (obj, keyPath) => {
  if (!obj || !keyPath) return null;

  // If keyPath is an array, use the first element as the key
  const key = Array.isArray(keyPath) ? keyPath[0] : keyPath;

  // Recursive function to search through nested objects
  const searchNested = (current, targetKey) => {
    if (!current || typeof current !== "object") return null;

    // Check if current object has the target key
    if (current.hasOwnProperty(targetKey)) {
      return current[targetKey];
    }

    // Search through all object properties recursively
    for (const prop in current) {
      if (typeof current[prop] === "object" && current[prop] !== null) {
        const result = searchNested(current[prop], targetKey);
        if (result !== null) return result;
      }
    }

    return null;
  };

  return searchNested(obj, key);
};

// Helper function to handle repeated components
const processRepeatedComponent = (
  formData,
  component,
  content,
  components,
  fileList
) => {
  if (
    !formData.repeatedComponentId ||
    !formData.repeatedComponentName ||
    !content
  ) {
    return null;
  }

  // console.log("Processing repeated component:", {
  //   repeatedComponentId: formData.repeatedComponentId,
  //   repeatedComponentName: formData.repeatedComponentName,
  //   hasRepeatedComponent: !!formData.repeatedComponent,
  //   contentKeys: Object.keys(content || {}),
  //   content: content,
  // });

  // Find the repeated component by ID or use the one provided in formData
  let repeatedComponent = null;

  // First try to use the repeatedComponent from formData if available
  if (formData.repeatedComponent) {
    repeatedComponent = formData.repeatedComponent;
    console.log("Using repeatedComponent from formData:", repeatedComponent);
  } else {
    // Fallback to finding by ID in components array
    repeatedComponent = components?.find(
      (c) => c.id === formData.repeatedComponentId
    );
  }

  if (!repeatedComponent) {
    console.warn(
      `Repeated component with ID ${formData.repeatedComponentId} not found in components array or formData`
    );
    return null;
  }
  // Get the array data for repeated content using nested search
  let repeatedData = findNestedContent(content, formData.repeatedComponentName);

  // If not found in nested search, try direct access for hierarchical structure
  if (!repeatedData && Array.isArray(formData.repeatedComponentName)) {
    const key = formData.repeatedComponentName[0];
    repeatedData = content?.[key];
  }

  console.log("Searching for key:", formData.repeatedComponentName);
  console.log("Found repeated data:", repeatedData);

  if (!Array.isArray(repeatedData)) {
    console.warn(
      `Repeated data for ${formData.repeatedComponentName} is not an array or not found. Found:`,
      repeatedData
    );
    return null;
  }

  console.log("Found repeated data:", repeatedData);

  // Generate HTML for each repeated item
  let repeatedHTML = "";
  repeatedData.forEach((item) => {
    let itemHTML = repeatedComponent.html_content;

    // Replace placeholders in this instance with item data
    itemHTML = replacePlaceholders(itemHTML, item, fileList);

    repeatedHTML += itemHTML + "\n";
  });

  // console.log("Generated repeated HTML:", repeatedHTML);
  return repeatedHTML;
};

// Global Preview HTML Generator - Unified function for both components and pages
export const generateGlobalPreviewHTML = (options = {}) => {
  const {
    data = [],
    components = [], // Available components for page type
    title = "Preview",
    customCSS = "",
    customJS = "",
    fullData = null,
    contentJSON = null,
    fileList = null,
  } = options;
  let bodyContent = "";
  let combinedCSS = "";
  let combinedJS = "";

  if (data?.length) {
    data.forEach((formData) => {
      const component = components?.find((c) => c.id === formData.id);

      if (component) {
        let componentHTML = component.html_content_withValue;
        // let componentHTML = component.html_content;
        combinedCSS += component.css_content || "";
        combinedJS += component.js_content || "";

        // Get component-specific content using hierarchical structure
        // const componentContent = getComponentContent(
        //   contentJSON,
        //   title,
        //   component.name
        // );

        // // Check if this is a repeated component
        // if (formData.repeatedComponentId && componentContent) {
        //   const repeatedHTML = processRepeatedComponent(
        //     formData,
        //     component,
        //     componentContent,
        //     components,
        //     fileList
        //   );

        //   if (repeatedHTML) {
        //     // Replace {{ITEMS}} placeholder with repeated content
        //     const itemsKey = Array.isArray(formData.repeatedComponentName)
        //       ? formData.repeatedComponentName[0]
        //       : formData.repeatedComponentName;
        //     componentHTML = componentHTML.replace(
        //       `{{${itemsKey}}}`,
        //       repeatedHTML
        //     );
        //   }
        //   // Also replace other placeholders with component-specific content
        //   componentHTML = replacePlaceholders(
        //     componentHTML,
        //     componentContent,
        //     fileList
        //   );
        // } else if (componentContent) {
        //   // Replace placeholders with component-specific content
        //   componentHTML = replacePlaceholders(
        //     componentHTML,
        //     componentContent,
        //     fileList
        //   );
        // }

        // Wrap with optional user CSS class
        const cssClass = formData.cssClass
          ? ` class="${formData.cssClass}"`
          : "";
        bodyContent += `<div${cssClass}>${componentHTML}</div>\n`;
      } else {
        let html = formData?.html_content_withValue || "";
        // let html = formData?.html_content || "";

        // Replace placeholders with content data for direct HTML
        // if (contentJSON) {
        //   html = replacePlaceholders(html, contentJSON, fileList);

        //   html = replacePlaceholders(html, contentJSON, fileList);

        //   // html = replacePlaceholders(html, contentJSON, fileList);
        // }

        bodyContent +=
          html ||
          '<p style="color: #6b7280; text-align: center; padding: 2rem;">No content to preview</p>';
        combinedCSS += formData?.css_content || "";
        combinedJS += formData?.js_content || "";
      }
    });

    combinedCSS += customCSS || "";
    combinedJS += customJS || "";
  } else {
    if (fullData) {
      bodyContent = fullData || "";
    } else {
      // Empty state for pages
      bodyContent = `
        <div style=" display: flex; align-items: center; justify-content: center; min-height: 100%; text-align: center; color: #6b7280;">
          <div>
            <div style="font-size: 3rem; margin-bottom: 1rem;">+</div>
            <p style="font-size: 1.125rem; margin-bottom: 0.5rem;">Drop components here</p>
            <p style="font-size: 0.875rem;">Drag components from the left panel to build your page</p>
          </div>
        </div>
      `;
    }
    // Still add custom CSS/JS even for empty pages
    combinedCSS += customCSS || "";
    combinedJS += customJS || "";
  }
  // ${generateTailwindCSS()}
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${title}</title>
  <!-- Tailwind CSS - Generated locally to avoid CDN warnings -->
<style>
      @font-face {
        font-family: Inter;
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)
          format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Inter;
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)
          format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Inter;
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/inter/v18/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2)
          format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Phudu;
        font-style: normal;
        font-weight: 600;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)
          format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Phudu;
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/phudu/v5/0FlaVPSHk0ya-5mYUB4.woff2)
          format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122,
          U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
    </style>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="//unpkg.com/alpinejs" defer></script>
    <script>
      tailwind.config = {
        content: ["./*.html"],
        prefix: "dw-",
        theme: {
          extend: {
            colors: {
              body: {
                bg: "#ffffff", // --body-bg-color
                text: "#333333", // --body-text-color
              },
              theme: {
                main: "#d5232b", // --theme-main-color
                mainColorShade: "#C41421",
                hoverBox: "rgba(82, 164, 206, 0.43)", // --theme-box-hover-color
                cardBorder: "#e0e0e0",
                cardBg: "#fafafa",
                divider: "#eee",
                lowOpacityTextColor: "#111827",
              },
            },
            fontFamily: {
              heading: ["Phudu", "sans-serif"], // --theme-heading-font
              text: ["Inter", "sans-serif"], // --theme-text-font
            },
            screens: {
              xs: "200px",
              smx: "376px",
              smm: "567px",
              mdx: "768px",
              mdl: "993px",
              lgx: "1171px",
              lgs: "1221px",
              lgm: "1281px",
              lgl: "1361px",
              xlx: "1400px",
              xl1: "1441px",
              xl2: "1600px",
              xxl: "1900px",
            },
          },
        },
        plugins: [],
      };
    </script>
  

  <style>
    /* Base styles */
    * { box-sizing: border-box; }
    html {
      height: 100%;
    }
    body {
      margin: 0;
      padding: 20px;
      font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
      line-height: 1.6;
      background: white;
      color: #374151;
    }

    /* Tailwind CSS classes with tw- prefix */
    .tw-bg-blue-500 { background-color: rgb(59 130 246) !important; }
    .tw-bg-blue-600 { background-color: rgb(37 99 235) !important; }
    .tw-bg-purple-600 { background-color: rgb(147 51 234) !important; }
    .tw-bg-white { background-color: rgb(255 255 255) !important; }
    .tw-bg-gray-50 { background-color: rgb(249 250 251) !important; }
    .tw-bg-gray-100 { background-color: rgb(243 244 246) !important; }
    .tw-bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important; }
    .tw-from-blue-600 { --tw-gradient-from: rgb(37 99 235); --tw-gradient-to: rgb(37 99 235 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important; }
    .tw-to-purple-600 { --tw-gradient-to: rgb(147 51 234) !important; }

    .tw-text-white { color: rgb(255 255 255) !important; }
    .tw-text-blue-600 { color: rgb(37 99 235) !important; }
    .tw-text-gray-600 { color: rgb(75 85 99) !important; }
    .tw-text-gray-800 { color: rgb(31 41 55) !important; }
    .tw-text-gray-900 { color: rgb(17 24 39) !important; }

    .tw-p-4 { padding: 1rem !important; }
    .tw-p-6 { padding: 1.5rem !important; }
    .tw-p-8 { padding: 2rem !important; }
    .tw-px-4 { padding-left: 1rem; padding-right: 1rem !important; }
    .tw-px-6 { padding-left: 1.5rem; padding-right: 1.5rem !important; }
    .tw-px-8 { padding-left: 2rem; padding-right: 2rem !important; }
    .tw-py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem !important; }
    .tw-py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem !important; }
    .tw-py-4 { padding-top: 1rem; padding-bottom: 1rem !important; }
    .tw-py-16 { padding-top: 4rem; padding-bottom: 4rem !important; }
    .tw-py-20 { padding-top: 5rem; padding-bottom: 5rem !important; }

    .tw-m-4 { margin: 1rem !important; }
    .tw-mx-auto { margin-left: auto; margin-right: auto !important; }
    .tw-mt-2 { margin-top: 0.5rem !important; }
    .tw-mt-4 { margin-top: 1rem !important; }
    .tw-mb-4 { margin-bottom: 1rem !important; }
    .tw-mb-6 { margin-bottom: 1.5rem !important; }
    .tw-mb-8 { margin-bottom: 2rem !important; }

    .tw-rounded { border-radius: 0.25rem !important; }
    .tw-rounded-lg { border-radius: 0.5rem !important; }
    .tw-rounded-xl { border-radius: 0.75rem !important; }

    .tw-text-sm { font-size: 0.875rem; line-height: 1.25rem !important; }
    .tw-text-base { font-size: 1rem; line-height: 1.5rem !important; }
    .tw-text-lg { font-size: 1.125rem; line-height: 1.75rem !important; }
    .tw-text-xl { font-size: 1.25rem; line-height: 1.75rem !important; }
    .tw-text-2xl { font-size: 1.5rem; line-height: 2rem !important; }
    .tw-text-3xl { font-size: 1.875rem; line-height: 2.25rem !important; }
    .tw-text-4xl { font-size: 2.25rem; line-height: 2.5rem !important; }
    .tw-text-5xl { font-size: 3rem; line-height: 1 !important; }

    .tw-font-medium { font-weight: 500 !important; }
    .tw-font-semibold { font-weight: 600 !important; }
    .tw-font-bold { font-weight: 700 !important; }

    .tw-text-center { text-align: center !important; }
    .tw-text-left { text-align: left !important; }

    .tw-flex { display: flex !important; }
    .tw-grid { display: grid !important; }
    .tw-block { display: block !important; }
    .tw-inline-block { display: inline-block !important; }
    .tw-hidden { display: none !important; }

    .tw-items-center { align-items: center !important; }
    .tw-justify-center { justify-content: center !important; }
    .tw-justify-between { justify-content: space-between !important; }

    .tw-w-full { width: 100% !important; }
    .tw-w-auto { width: auto !important; }
    .tw-h-8 { height: 2rem !important; }
    .tw-h-auto { height: auto !important; }

    .tw-container { width: 100%; margin-left: auto; margin-right: auto !important; }
    @media (min-width: 640px) { .tw-container { max-width: 640px !important; } }
    @media (min-width: 768px) { .tw-container { max-width: 768px !important; } }
    @media (min-width: 1024px) { .tw-container { max-width: 1024px !important; } }
    @media (min-width: 1280px) { .tw-container { max-width: 1280px !important; } }

    .tw-shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important; }
    .tw-shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important; }
    .tw-shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important; }
    .tw-shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important; }

    .tw-border { border-width: 1px !important; }
    .tw-border-gray-200 { border-color: rgb(229 231 235) !important; }
    .tw-border-gray-300 { border-color: rgb(209 213 219) !important; }

    .tw-transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }
    .tw-transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms !important; }

    .tw-hover\\:tw-bg-gray-100:hover { background-color: rgb(243 244 246) !important; }
    .tw-hover\\:tw-text-blue-600:hover { color: rgb(37 99 235) !important; }

    /* Grid classes */
    .tw-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
    .tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
    .tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
    .tw-gap-4 { gap: 1rem !important; }
    .tw-gap-6 { gap: 1.5rem !important; }
    .tw-gap-8 { gap: 2rem !important; }

    @media (min-width: 768px) {
      .tw-md\\:tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
      .tw-md\\:tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
      .tw-md\\:tw-block { display: block !important; }
    }

    /* Custom component styles */
    ${combinedCSS}
  </style>
</head>
<body>
  ${bodyContent}
  <script>
    try {
      ${combinedJS}
    } catch(e) {
      console.error('JavaScript error:', e);
    }
  </script>
</body>
</html>`;
};

// Legacy function for backward compatibility
export const getPreviewHTML = (components) => {
  return generateGlobalPreviewHTML({
    type: "component",
    data: components,
    title: "Component Preview",
  });
};

// Device configurations for responsive preview
export const deviceConfigs = (deviceType) => {
  return {
    laptop: {
      width: "100%",
      height: "600px",
      icon: (
        <Monitor
          color={deviceType === "laptop" ? "#2563EB" : "#6B7280"}
          className="tw-w-4 tw-h-4"
        />
      ),
      label: "Desktop",
      description: "1200 × 800",
      scale: 1,
    },

    tablet: {
      // width: "95%",
      width: "768px",
      height: "600px", // Reduced height for better fit
      icon: (
        <Tablet
          color={deviceType === "tablet" ? "#2563EB" : "#6B7280"}
          className="tw-w-4 tw-h-4"
        />
      ),
      label: "Tablet",
      description: "768 × 600",
      scale: 1,
    },
    mobile: {
      width: "375px",
      height: "667px",
      icon: (
        <Smartphone
          color={deviceType === "mobile" ? "#2563EB" : "#6B7280"}
          className="tw-w-4 tw-h-4"
        />
      ),
      label: "Mobile",
      description: "375 × 667",
      scale: 1,
    },
  };
};
