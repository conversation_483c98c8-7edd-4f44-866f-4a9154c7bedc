import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { useSidebar } from "../../contexts/SidebarContext";
import logoImg from "/img/dream-logo.png";
import {
  LayoutDashboard,
  FolderTree,
  Component as Components,
  FileText,
  Layout,
  Globe,
  Users,
  LogOut,
  ChevronLeft,
  ChevronRight,
  Activity,
  Settings,
} from "lucide-react";
import { Button, Image, Drawer, Tooltip, Menu } from "antd";

const Sidebar = () => {
  const { user, logout } = useAuth();
  const {
    isCollapsed,
    isMobile,
    isMobileOpen,
    toggleCollapse,
    closeMobileMenu,
  } = useSidebar();
  const navigate = useNavigate();
  const location = useLocation();

  const navigationItems = [
    {
      key: "/",
      label: "Dashboard",
      icon: <LayoutDashboard className="tw-w-5 tw-h-5" />,
      adminOnly: false,
    },
    {
      key: "/categories",
      label: "Categories",
      icon: <FolderTree className="tw-w-5 tw-h-5" />,
      adminOnly: true,
    },
    {
      key: "/components",
      label: "Components",
      icon: <Components className="tw-w-5 tw-h-5" />,
      adminOnly: false,
    },
    {
      key: "/pages",
      label: "Pages",
      icon: <FileText className="tw-w-5 tw-h-5" />,
      adminOnly: false,
    },
    {
      key: "/dynamic-pages",
      label: "Dynamic Pages",
      icon: <Settings className="tw-w-5 tw-h-5" />,
      adminOnly: false,
    },
    {
      key: "/templates",
      label: "Templates",
      icon: <Layout className="tw-w-5 tw-h-5" />,
      adminOnly: false,
    },
    {
      key: "/websites",
      label: "Websites",
      icon: <Globe className="tw-w-5 tw-h-5" />,
      adminOnly: false,
    },
    {
      key: "/activity-logs",
      label: "Activity Logs",
      icon: <Activity className="tw-w-5 tw-h-5" />,
      adminOnly: true,
    },
    {
      key: "/users",
      label: "Users",
      icon: <Users className="tw-w-5 tw-h-5" />,
      adminOnly: true,
    },
  ];

  const filteredItems = navigationItems.filter(
    (item) => !item.adminOnly || user?.role === "admin"
  );

  const handleMenuClick = ({ key }) => {
    navigate(key);
    if (isMobile) {
      closeMobileMenu();
    }
  };

  const selectedKeys = [location.pathname];

  // Desktop Sidebar Component
  const DesktopSidebar = () => (
    <div
      className={`sidebar-desktop tw-bg-white tw-shadow-lg tw-h-screen tw-fixed tw-left-0 tw-top-0 tw-z-40 sidebar-collapse-transition ${
        isCollapsed ? "tw-w-20" : "tw-w-64"
      }`}
    >
      {/* Logo Section */}
      <div
        className={`tw-border-b tw-border-gray-200 tw-relative ${
          isCollapsed ? "tw-p-4" : "tw-p-[25px]"
        }`}
      >
        <div className="tw-flex tw-items-center tw-justify-center">
          {!isCollapsed && (
            <Image
              preview={false}
              src={logoImg}
              alt="Dream Builder"
              className="tw-w-auto tw-h-auto tw-aspect-auto tw-object-contain"
            />
          )}
          {isCollapsed && (
            <div className="tw-w-12 tw-h-12 tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-rounded-lg tw-flex tw-items-center tw-justify-center">
              <span className="tw-text-white tw-font-bold tw-text-sm">DB</span>
            </div>
          )}
        </div>

        {/* Collapse Button */}
        <Tooltip
          title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          placement="right"
        >
          <Button
            type="primary"
            shape="circle"
            size="small"
            icon={
              isCollapsed ? (
                <ChevronRight size={14} />
              ) : (
                <ChevronLeft size={14} />
              )
            }
            onClick={toggleCollapse}
            className="sidebar-collapse-btn tw-absolute tw-right-[-12px] tw-top-[83%] tw-bg-[#2563EB] hover:!tw-bg-[#2563EB] tw-border-[#2563EB] tw-shadow-lg"
            style={{
              width: "24px",
              height: "24px",
              minWidth: "24px",
              // display: "flex",
              // alignItems: "center",
              // justifyContent: "center",
            }}
          />
        </Tooltip>
      </div>

      {/* Navigation */}
      <div className={`tw-mt-6 ${isCollapsed ? "tw-px-1" : "tw-px-2"}`}>
        <Menu
          mode="inline"
          selectedKeys={selectedKeys}
          onClick={handleMenuClick}
          inlineCollapsed={isCollapsed}
          items={filteredItems}
          className="tw-border-0 tw-bg-transparent"
          style={{
            backgroundColor: "transparent",
            border: "none",
          }}
          theme="light"
        />
      </div>

      {/* Logout Button */}
      <div
        className={`tw-absolute tw-bottom-0 tw-w-full tw-border-t tw-border-gray-200 ${
          isCollapsed ? "tw-p-2" : "tw-p-4"
        }`}
      >
        <Tooltip title={isCollapsed ? "Log Out" : ""} placement="right">
          <button
            onClick={logout}
            className={`!tw-text-[#FB3748] tw-flex tw-items-center tw-w-full tw-text-sm tw-hover:tw-bg-gray-200 tw-rounded-lg tw-transition-colors ${
              isCollapsed
                ? "tw-px-3 tw-py-3 tw-justify-center"
                : "tw-px-3 tw-py-2"
            }`}
          >
            <LogOut
              color="#FB3748"
              className={`tw-w-4 tw-h-4 ${!isCollapsed ? "tw-mr-2" : ""}`}
            />
            {!isCollapsed && "Log Out"}
          </button>
        </Tooltip>
      </div>
    </div>
  );

  // Mobile Sidebar Component (Drawer)
  const MobileSidebar = () => (
    <Drawer
      title={
        <div className="tw-flex tw-items-center">
          <Image
            preview={false}
            src={logoImg}
            alt="Dream Builder"
            className="tw-w-auto tw-h-auto tw-aspect-auto tw-object-contain"
            style={{ maxHeight: "32px" }}
          />
        </div>
      }
      placement="left"
      onClose={closeMobileMenu}
      open={isMobileOpen}
      width={280}
      className="tw-mobile-sidebar"
      styles={{
        body: { padding: 0 },
        header: { borderBottom: "1px solid #f0f0f0" },
      }}
    >
      <div className="tw-mt-4 tw-px-2">
        <Menu
          mode="inline"
          selectedKeys={selectedKeys}
          onClick={handleMenuClick}
          items={filteredItems}
          className="tw-border-0 tw-bg-transparent"
          style={{
            backgroundColor: "transparent",
            border: "none",
          }}
          theme="light"
        />
      </div>

      <div className="tw-absolute tw-bottom-0 tw-w-full tw-p-4 tw-border-t tw-border-gray-200">
        <button
          onClick={() => {
            logout();
            closeMobileMenu();
          }}
          className="!tw-text-[#FB3748] tw-flex tw-items-center tw-w-full tw-px-3 tw-py-2 tw-text-sm tw-hover:tw-bg-gray-200 tw-rounded-lg tw-transition-colors"
        >
          <LogOut color="#FB3748" className="tw-w-4 tw-h-4 tw-mr-2" />
          Log Out
        </button>
      </div>
    </Drawer>
  );

  // Main render - show desktop or mobile sidebar based on screen size
  return (
    <>
      {!isMobile && <DesktopSidebar />}
      {isMobile && <MobileSidebar />}
    </>
  );
};

export default Sidebar;
