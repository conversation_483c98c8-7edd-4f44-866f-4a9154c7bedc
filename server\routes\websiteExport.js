import express from "express";
import fs from "fs";
import path from "path";
import archiver from "archiver";
import multer from "multer";
import { fileURLToPath } from "url";
import { dirname } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  dest: path.join(__dirname, "../temp/uploads"),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
});

// Enhanced website export route
router.post("/website", upload.any(), async (req, res) => {
  try {
    console.log("Starting enhanced website export...");

    // Parse export data
    const exportData = JSON.parse(req.body.exportData);
    const { pages, templateObj, bradingDetails, fileList, exportMetadata } = exportData;

    console.log(`Exporting ${exportMetadata.totalPages} pages (${exportMetadata.staticPages} static, ${exportMetadata.dynamicPages} dynamic)`);

    // Create temporary directory for website files
    const tempDir = path.join(__dirname, "../temp", `website-${Date.now()}`);
    const websiteDir = path.join(tempDir, "website");
    const assetsDir = path.join(websiteDir, "assets");

    // Create directories
    fs.mkdirSync(websiteDir, { recursive: true });
    fs.mkdirSync(assetsDir, { recursive: true });

    // Process uploaded assets
    const assetMap = {};
    if (req.files) {
      req.files.forEach(file => {
        if (file.fieldname.startsWith('assets_')) {
          const assetKey = file.fieldname.replace('assets_', '');
          const assetPath = path.join(assetsDir, file.originalname);
          fs.copyFileSync(file.path, assetPath);
          assetMap[assetKey] = `./assets/${file.originalname}`;

          // Clean up temp file
          fs.unlinkSync(file.path);
        }
      });
    }

    // Generate HTML files for each page
    const generatedFiles = [];

    for (const page of pages) {
      try {
        const htmlContent = generatePageHTML(page, bradingDetails, assetMap);
        const filename = getPageFilename(page);
        const filePath = path.join(websiteDir, filename);

        // Create subdirectories for dynamic pages if needed
        const dir = path.dirname(filePath);
        if (dir !== websiteDir) {
          fs.mkdirSync(dir, { recursive: true });
        }

        fs.writeFileSync(filePath, htmlContent);
        generatedFiles.push(filename);

        console.log(`Generated: ${filename}`);
      } catch (error) {
        console.error(`Error generating page ${page.name}:`, error);
      }
    }

    // Generate sitemap.xml
    const sitemapContent = generateSitemap(pages);
    fs.writeFileSync(path.join(websiteDir, "sitemap.xml"), sitemapContent);

    // Generate robots.txt
    const robotsContent = generateRobotsTxt();
    fs.writeFileSync(path.join(websiteDir, "robots.txt"), robotsContent);

    // Create ZIP file
    const zipPath = path.join(__dirname, "../exports", `website-${Date.now()}.zip`);

    // Ensure exports directory exists
    fs.mkdirSync(path.dirname(zipPath), { recursive: true });

    await createZipFile(websiteDir, zipPath);

    // Clean up temporary directory
    fs.rmSync(tempDir, { recursive: true, force: true });

    // Send response
    res.json({
      message: "Website exported successfully",
      downloadUrl: `/exports/${path.basename(zipPath)}`,
      filename: path.basename(zipPath),
      metadata: {
        ...exportMetadata,
        generatedFiles: generatedFiles.length,
        zipSize: fs.statSync(zipPath).size,
      },
    });

  } catch (error) {
    console.error("Export error:", error);
    res.status(500).json({
      error: "Failed to export website",
      details: error.message
    });
  }
});

// Helper function to generate page HTML
function generatePageHTML(page, bradingDetails, assetMap) {
  let htmlContent = page.full_page_content || "";

  // Ensure we have valid HTML structure
  if (!htmlContent.includes('<!DOCTYPE html>')) {
    htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${page.name || 'Page'}</title>
</head>
<body>
  ${htmlContent}
</body>
</html>`;
  }

  // Replace asset placeholders with actual paths
  Object.entries(assetMap).forEach(([key, path]) => {
    const regex = new RegExp(`\\$\\{${key}\\}`, "g");
    htmlContent = htmlContent.replace(regex, path);
  });

  // Apply branding colors and styles
  if (bradingDetails) {
    htmlContent = applyBrandingToHTML(htmlContent, bradingDetails);
  }

  return htmlContent;
}

// Helper function to get page filename based on type and URL
function getPageFilename(page) {
  if (page.type === "static") {
    // Static pages: /pagename -> pagename.html
    const pageName = page.url?.replace(/^\//, "") || page.name;
    return pageName === "" || pageName === "home" ? "index.html" : `${pageName}.html`;
  } else if (page.type === "dynamic") {
    // Dynamic pages: /category/slug -> category/slug.html
    const urlParts = page.url?.split("/").filter(Boolean) || [];
    if (urlParts.length >= 2) {
      const category = urlParts[0];
      const slug = urlParts[1];
      return `${category}/${slug}.html`;
    }
    return `${page.name}.html`;
  }
  return `${page.name || "page"}.html`;
}

// Helper function to apply branding to HTML
function applyBrandingToHTML(html, branding) {
  // Replace CSS custom properties with branding values
  const brandingCSS = `
    <style>
      :root {
        --primary-color: ${branding.primaryColor || "#3B82F6"};
        --secondary-color: ${branding.secondaryColor || "#3B82F6"};
        --background-color: ${branding.backgroundColor || "#ffffff"};
        --text-color: ${branding.textColor || "#000000"};
        --border-color: ${branding.borderColor || "#d9d9d9"};
      }
    </style>
  `;

  // Insert branding CSS before closing head tag
  if (html.includes("</head>")) {
    html = html.replace("</head>", `${brandingCSS}</head>`);
  }

  return html;
}

// Helper function to generate sitemap.xml
function generateSitemap(pages) {
  const baseUrl = "https://yourwebsite.com"; // This should be configurable

  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

  pages.forEach(page => {
    const url = page.url?.startsWith("/") ? page.url : `/${page.url || page.name}`;
    sitemap += `
  <url>
    <loc>${baseUrl}${url}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${page.type === "static" && (url === "/" || url === "/home") ? "1.0" : "0.8"}</priority>
  </url>`;
  });

  sitemap += `
</urlset>`;

  return sitemap;
}

// Helper function to generate robots.txt
function generateRobotsTxt() {
  return `User-agent: *
Allow: /

Sitemap: https://yourwebsite.com/sitemap.xml`;
}

// Helper function to create ZIP file
function createZipFile(sourceDir, outputPath) {
  return new Promise((resolve, reject) => {
    const output = fs.createWriteStream(outputPath);
    const archive = archiver("zip", { zlib: { level: 9 } });

    output.on("close", () => {
      console.log(`ZIP created: ${archive.pointer()} total bytes`);
      resolve();
    });

    archive.on("error", (err) => {
      reject(err);
    });

    archive.pipe(output);
    archive.directory(sourceDir, false);
    archive.finalize();
  });
}

export default router;
