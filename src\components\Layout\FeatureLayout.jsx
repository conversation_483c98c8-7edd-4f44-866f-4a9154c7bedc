import React from "react";
import { Outlet } from "react-router-dom";
import { Typography } from "antd";

const { Title, Text } = Typography;

const FeatureLayout = ({ title, subtitle, children }) => {
  return (
    <div className="tw-min-h-screen tw-bg-gray-50">
      {/* Feature Layout - Full width without sidebar */}
      <div className="tw-w-full">
        {/* Optional Header for Feature Layout */}
        {(title || subtitle) && (
          <div className="tw-bg-white tw-border-b tw-border-gray-200 tw-px-6 tw-py-4">
            <div className="tw-max-w-7xl tw-mx-auto">
              {title && (
                <Title level={2} className="!tw-mb-1 !tw-text-gray-900">
                  {title}
                </Title>
              )}
              {subtitle && (
                <Text type="secondary" className="tw-text-base">
                  {subtitle}
                </Text>
              )}
            </div>
          </div>
        )}
        
        {/* Main Content */}
        <main className="tw-w-full">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default FeatureLayout;
