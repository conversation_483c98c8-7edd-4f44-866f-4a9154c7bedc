// Helper: deep clone + replace ${slug} everywhere in string values
export const hydrateOldContentData = (source, slugObj) => {
  const replaceInString = (s) => s?.replace(/\$\{slug\}/g, slugObj.slug);

  const walk = (node, parentNode) => {
    if (node == null) return node;

    if (typeof node === "string") {
      // check if the string contains ${slug} if yes then replace it with slugObj.slug else replace it with slugObj.value
      // after replacing add another key with that value replce

      if (node.includes("${slug}") && node?.toString()) {
        // only key value which is in between ${} is key and value is outside of ${}
        const key = replaceInString(node)?.match(/\$\{([^}]+)\}/)?.[1];
        parentNode[parentNode.key] = replaceInString(key);
        return replaceInString(node);
      }
      // else {
      //   return node.replace(/\$\{slug\}/g, slugObj.name);
      // }
      //   return replaceInString(node);
    }

    if (Array.isArray(node)) {
      return node.map(walk);
    }

    if (typeof node === "object") {
      const out = {};
      for (const [k, v] of Object.entries(node)) {
        out[k] = walk(v, node);
      }
      return out;
    }

    return node; // numbers, booleans, etc.
  };

  return walk(source);
};

export const processStringValue = (s, slug, label) => {
  if (typeof s !== "string") return { text: s, injections: [] };

  // 1) Handle nested tokens: ${ ... } allowing one {...} inside
  const TOKEN_RE = /\$\{(?:[^{}]|\{[^{}]*\})*\}/g;
  let injections = [];
  let text = s.replace(TOKEN_RE, (whole) => {
    const inside = whole.slice(2, -1); // content without ${}
    if (inside.includes("${slug}")) {
      // Replace only inner ${slug} -> slug
      const replacedInside = inside.replace(/\$\{slug\}/g, slug);
      const newToken = "${" + replacedInside + "}";

      // Record injection key (the content inside ${} after replacement)
      // e.g., ${${slug}_state} -> inject "chicago_state"
      injections.push(replacedInside);
      return newToken;
    }
    return whole; // untouched token
  });

  // 2) Replace any leftover plain ${slug} -> slug
  text = text.replace(/\$\{slug\}/g, label);
  return { text, injections };
};

export const transformForSlug = (node, slug, label) => {
  if (node == null) return node;
  if (typeof node === "string") {
    // No place to inject here; just return processed text
    return processStringValue(node, slug, label).text;
  }

  if (Array.isArray(node)) {
    return node.map((item) => transformForSlug(item, slug, label));
  }

  if (typeof node === "object") {
    const out = {};
    for (const [k, v] of Object.entries(node)) {
      if (typeof v === "string") {
        const { text, injections } = processStringValue(v, slug, label);
        out[k] = text;

        // Add discovered injection keys at this same object level
        for (const injKey of injections) {
          if (!(injKey in out)) {
            out[injKey] = ""; // default empty string
          }
        }
      } else {
        out[k] = transformForSlug(v, slug, label);
      }
    }
    return out;
  }

  // numbers, booleans, etc.
  return node;
};

export const buildAllSlugJSON = (oldContentData, slugsAll) => {
  const bySlug = {};
  for (const { slug, name, label } of slugsAll) {
    bySlug[slug] = transformForSlug(oldContentData, slug, label);
  }
  return bySlug;
};
// =========================
// Matches ${var_name} where var_name can include a-z, 0-9, _, and dashes
const VAR_TOKEN_RE = /\$\{([a-z0-9_\-]+)\}/gi;

/**
 * Replace ${var} in a string using a vars map. Unknown vars are kept as-is.
 */
export const resolveString = (str, vars) => {
  if (typeof str !== "string") return str;
  return str.replace(VAR_TOKEN_RE, (_, key) =>
    Object.prototype.hasOwnProperty.call(vars, key)
      ? String(vars[key])
      : `\${${key}}`
  );
};
export const buildVarsOnlyForSlug = (newConObj, slug) => {
  const out = {};
  const slugObj = newConObj?.[slug] || {};
  for (const [sectionName, sectionObj] of Object.entries(slugObj)) {
    if (
      !sectionObj ||
      typeof sectionObj !== "object" ||
      Array.isArray(sectionObj)
    )
      continue;

    const vars = {};
    // a) keys already injected
    for (const [k, v] of Object.entries(sectionObj)) {
      if (k.startsWith(`${slug}_`)) vars[k] = v;
    }
    // b) discover tokens `${slug_*}` inside strings
    for (const v of Object.values(sectionObj)) {
      if (typeof v !== "string") continue;
      let m;
      while ((m = VAR_TOKEN_RE.exec(v))) {
        const token = m[1];
        if (token.startsWith(`${slug}_`) && !(token in vars)) {
          vars[token] = ""; // ensure the var exists
        }
      }
    }

    if (Object.keys(vars).length) out[sectionName] = vars;
  }
  return out;
};
export const resolveContentForSlug = (newConObj, slug, varsBySection) => {
  const src = newConObj?.[slug] || {};
  const clone = JSON.parse(JSON.stringify(src)); // deep copy for a pure “view”

  for (const [sectionName, sectionObj] of Object.entries(clone)) {
    const scope = varsBySection?.[sectionName] || {};
    for (const [k, v] of Object.entries(sectionObj)) {
      if (typeof v === "string" && !k.startsWith(`${slug}_`)) {
        sectionObj[k] = resolveString(v, scope);
      }
    }
  }
  return clone;
};

export const buildVarsForAllSlugs = (newConObj) => {
  const out = {};

  for (const [slug, slugObj] of Object.entries(newConObj || {})) {
    if (!slugObj || typeof slugObj !== "object") continue;

    for (const [sectionName, sectionObj] of Object.entries(slugObj)) {
      if (
        !sectionObj ||
        typeof sectionObj !== "object" ||
        Array.isArray(sectionObj)
      )
        continue;

      const vars = {};

      // (a) keys already injected: section keys that begin with `${slug}_`
      for (const [k, v] of Object.entries(sectionObj)) {
        if (k.startsWith(`${slug}_`)) vars[k] = v;
      }

      // (b) discover tokens ${slug_*} inside string values
      for (const v of Object.values(sectionObj)) {
        if (typeof v !== "string") continue;
        VAR_TOKEN_RE.lastIndex = 0; // reset before each new string
        let m;
        while ((m = VAR_TOKEN_RE.exec(v))) {
          const token = m[1]; // e.g. "mySlug_title"
          if (token.startsWith(`${slug}_`) && !(token in vars)) {
            vars[token] = ""; // ensure the var exists
          }
        }
      }

      if (Object.keys(vars).length) {
        if (!out[slug]) out[slug] = {};
        out[slug][sectionName] = vars;
      }
    }
  }
  return out;
};

export const dynamicContentForm = {
  "dynamic-city-6pm": {
    sitemapLabel: "City wise",
    navigationType: "Multi Column",
    sections: [
      {
        sectionLabel: "Charter Buses",
        sectionItems: [
          {
            label: "Surat",
            slug: "surat",
          },
          {
            label: "Ahmedabad",
            slug: "ahmedabad",
          },
          {
            label: "Mumbai",
            slug: "mumbai",
          },
        ],
      },
      {
        sectionLabel: "Specialty Vehicles",
        sectionItems: [
          {
            label: "Sprinter Van Rental",
            slug: "sprinter-van-rental-with-driver",
          },
          {
            label: "Party Bus Rental",
            slug: "party-bus-rental",
          },
          {
            label: "Sprinter Limo Rental",
            slug: "sprinter-limo-rental",
          },
          {
            label: "School Bus Rental",
            slug: "school-bus-rental",
          },
        ],
      },
      {
        sectionLabel: "Minibuses",
        sectionItems: [
          {
            label: "15 Passenger Minibus",
            slug: "15-passenger-minibus-rental",
          },
          {
            label: "18 Passenger Minibus",
            slug: "18-passenger-minibus-rental",
          },
          {
            label: "20 Passenger Minibus",
            slug: "20-passenger-minibus-rental",
          },
          {
            label: "25 Passenger Minibus",
            slug: "25-passenger-minibus-rental",
          },
          {
            label: "28 Passenger Minibus",
            slug: "28-passenger-minibus-rental",
          },
          {
            label: "30 Passenger Minibus",
            slug: "30-passenger-minibus-rental",
          },
          {
            label: "35 Passenger Minibus",
            slug: "35-passenger-minibus-rental",
          },
        ],
      },
    ],
    columnCount: "2",
  },
};

export const resolveTemplate = (input, content, maxDepth = 20) => {
  if (typeof input !== "string") return input;

  // Resolve ${...} inside a *name* (e.g., "${slug}_state" -> "surat_state")
  function resolveInName(name) {
    let out = name,
      guard = 0;
    while (guard++ < maxDepth) {
      const next = out.replace(/\$\{([^}]+)\}/g, (_, inner) => {
        const key = inner.trim();
        return Object.prototype.hasOwnProperty.call(content, key)
          ? String(content[key])
          : "${" + key + "}"; // keep unresolved placeholders
      });
      if (next === out) break;
      out = next;
    }
    return out;
  }

  let result = input;
  let changed = true;
  let rounds = 0;

  while (changed && rounds++ < maxDepth) {
    changed = false;

    result = result.replace(/\$\{([^}]+)\}/g, (full, expr) => {
      // 1) First, resolve any ${...} inside the placeholder *name*
      const resolvedName = resolveInName(expr.trim());

      // 2) If that final name exists in content, replace the whole placeholder
      if (Object.prototype.hasOwnProperty.call(content, resolvedName)) {
        changed = true;
        return String(content[resolvedName]);
      }

      // 3) Otherwise, leave it as-is for a later round
      return "${" + expr + "}";
    });
  }

  return result;
};

// function extractVariables(str) {
//   const regex = /\$\{([^}]+)\}/g;
//   let match;
//   const vars = new Set();

//   while ((match = regex.exec(str)) !== null) {
//     let expr = match[1].trim();

//     // If it's a nested template like ${${slug}_state}
//     // recursively extract inner variables
//     if (expr.startsWith("${") && expr.endsWith("}")) {
//       vars.add(expr);
//       vars.forEach((v) => extractVariables(v).forEach((x) => vars.add(x)));
//     } else {
//       vars.add(expr);
//     }
//   }

//   return Array.from(vars);
// }

// extractVariables("${slug} ${${slug}_state}");
function extractVariables(str) {
  const results = [];
  const seen = new Set();

  const add = (v) => {
    if (!seen.has(v)) {
      seen.add(v);
      results.push(v);
    }
  };

  function scan(s, from = 0) {
    let i = from;
    while (i < s.length) {
      const start = s.indexOf("${", i);
      if (start === -1) break;

      let j = start + 2; // after "${"
      let depth = 1;

      // walk forward, tracking nested ${...}
      while (j < s.length && depth > 0) {
        if (s[j] === "$" && s[j + 1] === "{") {
          depth++;
          j += 2;
          continue;
        }
        if (s[j] === "}") {
          depth--;
          j++;
          continue;
        }
        j++;
      }

      if (depth === 0) {
        const expr = s.slice(start + 2, j - 1).trim(); // content inside ${...}
        // first, collect inner placeholders inside expr
        scan(expr);
        // then, add the whole expr (e.g., "${slug}_state")
        add(expr);
        i = j;
      } else {
        // unmatched brace; stop
        break;
      }
    }
  }

  scan(str);
  return results;
}

export const findAndReplaceVariable = ({
  str,
  content,
  extraVariable = [],
  isDirectVariable = true,
  mediaFiles = {},
  comVariable = {},
}) => {
  if (!str) return false;
  const variables = extractVariables(str);
  // console.log({ variables }, "variables");
  const directVariable = variables?.filter(
    (variable) => extractVariables(variable)?.length == 0
  );
  // console.log({ directVariable }, "directVariable");
  directVariable.map((variable) => {
    if (
      !variable.includes("_img-") &&
      !(variable in comVariable) &&
      variable != "slug"
    ) {
      comVariable[variable] = "";
    }

    if (!isDirectVariable) {
      extraVariable.push(variable);
      if (variable in content) {
        comVariable[variable] = content[variable];
      }
    }

    if (variable.includes("_img-") && variable in mediaFiles) {
      str = str?.replace(
        new RegExp(`\\$\\{${variable}\\}`, "g"),
        mediaFiles[variable]
      );
      // console.log({ variable, mediaFiles }, "variable_detected", str);
    } else if (variable in content) {
      str = str?.replace(
        new RegExp(`\\$\\{${variable}\\}`, "g"),
        content[variable]
      );
    }
  });
  if (directVariable?.length != variables.length) {
    return findAndReplaceVariable({
      str,
      content,
      extraVariable,
      isDirectVariable: false,
      mediaFiles,
      comVariable,
    });
  }
  return { str, extraVariable };
};

export const deepMerge = (target, source) => {
  for (const key in source) {
    if (
      typeof source[key] === "object" &&
      source[key] !== null &&
      !Array.isArray(source[key])
    ) {
      if (!target[key]) target[key] = {};
      deepMerge(target[key], source[key]);
    } else {
      target[key] = source[key];
    }
  }
  return target;
};

const DEFAULTS = {
  navigationType: "Single Column",
  columnCount: 0,
};

const isPlainObject = (v) => v && typeof v === "object" && !Array.isArray(v);

const isEmptyScalar = (v) =>
  v === undefined || v === null || v === "" || v === 0;

const clone = (v) => {
  if (Array.isArray(v)) return v.map(clone);
  if (isPlainObject(v))
    return Object.fromEntries(
      Object.entries(v).map(([k, val]) => [k, clone(val)])
    );
  return v;
};

export const mergePreferExisting = (target, source, defaults = DEFAULTS) => {
  // Handle arrays: keep target if it has items, else take source
  if (Array.isArray(target) || Array.isArray(source)) {
    if (Array.isArray(target) && target.length > 0) return clone(target);
    return Array.isArray(source) ? clone(source) : clone(target);
  }

  // Handle plain objects
  if (isPlainObject(target) && isPlainObject(source)) {
    const out = { ...target };
    for (const key of Object.keys(source)) {
      const tVal = out[key];
      const sVal = source[key];

      if (isPlainObject(tVal) && isPlainObject(sVal)) {
        out[key] = mergePreferExisting(tVal, sVal, defaults);
        continue;
      }

      if (Array.isArray(tVal) || Array.isArray(sVal)) {
        out[key] = mergePreferExisting(tVal, sVal, defaults);
        continue;
      }

      // Scalars
      const def = Object.prototype.hasOwnProperty.call(defaults, key)
        ? defaults[key]
        : undefined;
      const targetIsEmpty =
        isEmptyScalar(tVal) || (def !== undefined && tVal === def);

      if (targetIsEmpty && sVal !== undefined && sVal !== null && sVal !== "") {
        out[key] = clone(sVal);
      } // else keep existing tVal
    }

    // Add keys that exist only in source
    for (const key of Object.keys(source)) {
      if (!(key in out)) out[key] = clone(source[key]);
    }
    return out;
  }

  // Scalars: keep target if non-empty, else take source
  return isEmptyScalar(target) ? clone(source) : clone(target);
};
