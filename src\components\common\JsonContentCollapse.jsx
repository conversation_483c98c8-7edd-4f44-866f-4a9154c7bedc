import { Button, Collapse, Input, Tag } from "antd";
import { ChevronDown, Trash2 } from "lucide-react";
import React, { useMemo, useEffect, useState } from "react";

const { Panel } = Collapse;
const { TextArea } = Input;

// JsonContentCollapse now supports optional "expanded" prop to control
// expand/collapse state externally without changing existing design.
const JsonContentCollapse = React.memo(
  ({ itemList, expanded, onDeleteItem }) => {
    const allKeys = useMemo(
      () => itemList?.map((it) => it.key) || [],
      [itemList]
    );

    // Keep internal active keys to allow dynamic user toggling while
    // still responding to external expand/collapse all.
    const [activeKeys, setActiveKeys] = useState([]);
    // 1) React to expand toggle changes only
    useEffect(() => {
      if (typeof expanded === "boolean") {
        const target = expanded ? allKeys : [];
        setActiveKeys((prev) =>
          prev.length === target.length && prev.every((v, i) => v === target[i])
            ? prev
            : target
        );
      }
    }, [expanded]);

    // 2) If expanded is true, keep activeKeys in sync with new keys (e.g., after Add Item)
    useEffect(() => {
      if (expanded === true) {
        setActiveKeys((prev) =>
          prev.length === allKeys.length &&
          prev.every((v, i) => v === allKeys[i])
            ? prev
            : allKeys
        );
      }
    }, [allKeys, expanded]);

    return (
      <>
        <Collapse
          size="middle"
          // Keep defaultActiveKey behavior for uncontrolled usage when no external control
          className=""
          expandIcon={(panelProps) => {
            const { isActive, extra, isDelete, onDelete } = panelProps || {};
            return (
              <div className="tw-flex tw-space-x-1 tw-items-center">
                {isDelete && (
                  <Button
                    type="text"
                    danger
                    className=""
                    icon={<Trash2 className="tw-w-4 tw-h-4" />}
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete && onDelete();
                    }}
                  />
                )}

                {extra != undefined ? (
                  <Tag
                    className="tw-p-1 tw-px-2 tw-rounded-xl"
                    color={extra ? "purple" : "blue"}
                  >
                    {extra ? "Repeat" : "Single"}
                  </Tag>
                ) : (
                  ""
                )}
                <ChevronDown
                  className={`tw-w-4 tw-h-4 tw-text-gray-400 tw-transition-transform tw-duration-200 ${
                    isActive ? "tw-rotate-180" : ""
                  }`}
                />
              </div>
            );
          }}
          expandIconPosition="end"
          items={(itemList || []).map((it) => ({
            ...it,
            onDelete:
              it.onDelete ||
              (it.isDelete && onDeleteItem
                ? () => onDeleteItem(it)
                : undefined),
          }))}
          activeKey={activeKeys}
          onChange={(keys) => setActiveKeys(keys)}
        />
      </>
    );
  }
);

export default JsonContentCollapse;
