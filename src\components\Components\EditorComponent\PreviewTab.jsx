import { Tooltip } from "antd";
import { Eye } from "lucide-react";
import React, { useState, useEffect, useRef } from "react";
import { deviceConfigs, generateGlobalPreviewHTML } from "../content";

// Device sizes for responsive preview (same as PagePreview)
const DEVICE_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1200, height: 800 },
};

const PreviewTab = ({ formData, generatePreview = null }) => {
  const [deviceType, setDeviceType] = useState("laptop"); // laptop, tablet, mobile
  const deviceConfig = deviceConfigs(deviceType);
  const [scale, setScale] = useState(1);
  const containerRef = useRef(null);

  // Get current device dimensions
  const { width: deviceWidth, height: deviceHeight } = DEVICE_SIZES[deviceType];

  // Scale calculation function (same as PagePreview)
  const recalcScale = () => {
    if (!containerRef.current) return;
    const bounds = containerRef.current.getBoundingClientRect();
    // Add padding to ensure device doesn't fill entire container
    const availableWidth = bounds.width - 30; // 15px padding on each side
    const availableHeight = bounds.height - 30; // 15px padding on top/bottom
    const widthScale = availableWidth / deviceWidth;
    const heightScale = availableHeight / deviceHeight;
    setScale(Math.min(widthScale, heightScale, 1)); // Don't scale up beyond 100%
  };

  // Update scale on mount & when device changes
  useEffect(() => {
    recalcScale();
    const resizeObserver = new ResizeObserver(recalcScale);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    return () => resizeObserver.disconnect();
  }, [deviceType, deviceWidth, deviceHeight]);
  return (
    <div className="tw-p-0 tw-mb-4">
      <div className="tw-flex tw-flex-col tw-items-center tw-mb-6">
        <div className="tw-flex tw-items-center tw-space-x-2  tw-rounded-lg  tw-mb-1">
          {Object.entries(deviceConfig)?.map(([key, config]) => (
            <Tooltip
              key={key}
              title={`${config.label} (${config.description})`}
            >
              <button
                type="button"
                onClick={() => setDeviceType(key)}
                className={`tw-flex tw-items-center tw-justify-center tw-w-8 tw-h-8 tw-rounded-md tw-transition-all tw-duration-200 ${
                  deviceType === key
                    ? "tw-bg-[#EAF0FD] tw-text-white tw-shadow-sm"
                    : "tw-text-gray-500 tw-hover:tw-bg-gray-100 tw-hover:tw-text-gray-700"
                }`}
              >
                {config.icon}
              </button>
            </Tooltip>
          ))}
        </div>
      </div>

      {/* Preview Container */}
      {formData?.html_content || generatePreview ? (
        <div
          ref={containerRef}
          className="tw-w-full tw-h-[34rem] tw-bg-gray-100 tw-flex tw-justify-center tw-items-center tw-overflow-hidden tw-relative tw-rounded-lg"
        >
          {/* Virtual device */}
          <div
            className="tw-bg-white tw-rounded-xl tw-shadow-xl tw-border tw-border-gray-200 tw-absolute tw-overflow-hidden"
            style={{
              width: `${deviceWidth}px`,
              height: `${deviceHeight}px`,
              transform: `scale(${scale})`,
              left: "50%",
              top: "50%",
              marginLeft: `-${deviceWidth / 2}px`,
              marginTop: `-${deviceHeight / 2}px`,
              transition: "transform 0.3s ease",
            }}
          >
            <div className="tw-relative tw-w-full tw-h-full tw-flex tw-flex-col tw-overflow-hidden tw-rounded-xl">
              {/* Device Frame Header (for mobile/tablet) */}
              {deviceType !== "laptop" && (
                <div className="tw-h-6 tw-bg-gray-100 tw-flex tw-items-center tw-justify-center tw-border-b tw-border-gray-200">
                  <div className="tw-flex tw-space-x-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-gray-400 tw-rounded-full"></div>
                    <div className="tw-w-2 tw-h-2 tw-bg-gray-400 tw-rounded-full"></div>
                    <div className="tw-w-2 tw-h-2 tw-bg-gray-400 tw-rounded-full"></div>
                  </div>
                </div>
              )}
              <iframe
                srcDoc={
                  generatePreview
                    ? generatePreview
                    : generateGlobalPreviewHTML({
                        type: "component",
                        data: [formData],
                        title: "Component Preview",
                      })
                }
                className="tw-w-full tw-h-full tw-border-0 tw-rounded-xl"
                title="Component Preview"
                style={{
                  height:
                    deviceType !== "laptop" ? "calc(100% - 24px)" : "100%",
                  background: "#fff",
                }}
              />
            </div>
          </div>
        </div>
      ) : (
        <div
          ref={containerRef}
          className="tw-w-full tw-h-[34rem] tw-bg-gray-100 tw-flex tw-justify-center tw-items-center tw-overflow-hidden tw-relative tw-rounded-lg"
        >
          {/* Virtual device */}
          <div
            className="tw-bg-white tw-rounded-xl tw-shadow-xl tw-border-2 tw-border-dashed tw-border-gray-300 tw-absolute tw-overflow-hidden"
            style={{
              width: `${deviceWidth}px`,
              height: `${deviceHeight}px`,
              transform: `scale(${scale})`,
              left: "50%",
              top: "50%",
              marginLeft: `-${deviceWidth / 2}px`,
              marginTop: `-${deviceHeight / 2}px`,
              transition: "transform 0.3s ease",
            }}
          >
            <div className="tw-relative tw-w-full tw-h-full tw-flex tw-flex-col tw-overflow-hidden tw-rounded-xl">
              {/* Device Frame Header (for mobile/tablet) */}
              {deviceType !== "laptop" && (
                <div className="tw-h-6 tw-bg-gray-100 tw-flex tw-items-center tw-justify-center tw-border-b tw-border-gray-200">
                  <div className="tw-flex tw-space-x-1">
                    <div className="tw-w-2 tw-h-2 tw-bg-gray-400 tw-rounded-full"></div>
                    <div className="tw-w-2 tw-h-2 tw-bg-gray-400 tw-rounded-full"></div>
                    <div className="tw-w-2 tw-h-2 tw-bg-gray-400 tw-rounded-full"></div>
                  </div>
                </div>
              )}

              {/* Empty State Content */}
              <div className="tw-h-full tw-flex tw-items-center tw-justify-center tw-p-6">
                <div className="tw-text-center">
                  <Eye className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                  <p className="tw-text-gray-500 tw-mb-2">
                    Add HTML content to see preview
                  </p>
                  <p className="tw-text-sm tw-text-gray-400">
                    Preview will be available in mobile, tablet, and desktop
                    views
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PreviewTab;
