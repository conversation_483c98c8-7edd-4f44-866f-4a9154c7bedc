/**
 * Asset Discovery Utility
 * Helps discover and manage project assets for export
 */

/**
 * Get all known project assets
 * This includes assets from public/asset and public/img folders
 */
export const getKnownProjectAssets = () => {
  return [
    // Assets from public/asset folder
    {
      path: '/asset/clifton-charter-bus.jpeg',
      filename: 'clifton-charter-bus.jpeg',
      type: 'image'
    },
    {
      path: '/asset/passaic-56-passenger-charter-bus.jpeg',
      filename: 'passaic-56-passenger-charter-bus.jpeg',
      type: 'image'
    },
    {
      path: '/asset/passaic-56-passenger_img-why-section-image.jpeg',
      filename: 'passaic-56-passenger_img-why-section-image.jpeg',
      type: 'image'
    },
    
    // Assets from public/img folder
    {
      path: '/img/dream-logo.png',
      filename: 'dream-logo.png',
      type: 'image'
    },
    {
      path: '/img/login-side.jpg',
      filename: 'login-side.jpg',
      type: 'image'
    }
  ];
};

/**
 * Discover assets by trying to fetch them
 * This can be extended to check for more assets dynamically
 */
export const discoverAdditionalAssets = async () => {
  const additionalAssets = [];
  
  // Common asset file extensions
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'];
  const documentExtensions = ['pdf', 'doc', 'docx', 'txt'];
  
  // Common asset names that might exist
  const commonAssetNames = [
    'logo',
    'favicon',
    'hero',
    'background',
    'banner',
    'icon',
    'thumbnail'
  ];
  
  // Common folders to check
  const assetFolders = ['/asset/', '/img/', '/images/', '/assets/'];
  
  // Try to discover assets (this is a basic implementation)
  // In a real scenario, you might have an API endpoint that lists assets
  for (const folder of assetFolders) {
    for (const name of commonAssetNames) {
      for (const ext of imageExtensions) {
        const assetPath = `${folder}${name}.${ext}`;
        try {
          const response = await fetch(assetPath, { method: 'HEAD' });
          if (response.ok) {
            additionalAssets.push({
              path: assetPath,
              filename: `${name}.${ext}`,
              type: 'image'
            });
          }
        } catch (error) {
          // Asset doesn't exist, continue
        }
      }
    }
  }
  
  return additionalAssets;
};

/**
 * Get asset replacement patterns for HTML content
 */
export const getAssetReplacementPatterns = () => {
  const knownAssets = getKnownProjectAssets();
  const patterns = [];
  
  // Create replacement patterns for each known asset
  knownAssets.forEach(asset => {
    patterns.push({
      from: new RegExp(asset.path.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
      to: `./assets/${asset.filename}`
    });
  });
  
  // Add general folder replacements
  patterns.push(
    { from: /\/asset\//g, to: './assets/' },
    { from: /\/img\//g, to: './assets/' },
    { from: /\/images\//g, to: './assets/' },
    { from: /\/assets\//g, to: './assets/' }
  );
  
  return patterns;
};

/**
 * Extract asset references from HTML content
 * This helps identify which assets are actually used
 */
export const extractAssetReferences = (htmlContent) => {
  const assetReferences = [];
  
  // Regular expressions to find asset references
  const patterns = [
    // img src attributes
    /src=["']([^"']*\.(jpg|jpeg|png|gif|svg|webp))[^"']*["']/gi,
    // CSS background images
    /background-image:\s*url\(["']?([^"')]*\.(jpg|jpeg|png|gif|svg|webp))[^"')]*["']?\)/gi,
    // link href for icons
    /href=["']([^"']*\.(ico|png|svg))[^"']*["']/gi
  ];
  
  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(htmlContent)) !== null) {
      const assetPath = match[1];
      if (assetPath && !assetReferences.includes(assetPath)) {
        assetReferences.push(assetPath);
      }
    }
  });
  
  return assetReferences;
};

/**
 * Validate if an asset exists and is accessible
 */
export const validateAsset = async (assetPath) => {
  try {
    const response = await fetch(assetPath, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    return false;
  }
};

/**
 * Get all assets that should be included in export
 * Combines known assets with discovered assets
 */
export const getAllExportAssets = async () => {
  const knownAssets = getKnownProjectAssets();
  const additionalAssets = await discoverAdditionalAssets();
  
  // Combine and deduplicate
  const allAssets = [...knownAssets];
  
  additionalAssets.forEach(asset => {
    const exists = allAssets.some(existing => existing.path === asset.path);
    if (!exists) {
      allAssets.push(asset);
    }
  });
  
  // Validate assets exist
  const validAssets = [];
  for (const asset of allAssets) {
    const isValid = await validateAsset(asset.path);
    if (isValid) {
      validAssets.push(asset);
    }
  }
  
  return validAssets;
};

/**
 * Create asset manifest for export
 */
export const createAssetManifest = (assets) => {
  return {
    version: '1.0.0',
    generatedAt: new Date().toISOString(),
    totalAssets: assets.length,
    assets: assets.map(asset => ({
      originalPath: asset.path,
      exportPath: `./assets/${asset.filename}`,
      filename: asset.filename,
      type: asset.type
    }))
  };
};
