/**
 * Asset Discovery Utility
 * Helps discover and manage project assets for export
 */

/**
 * Get all known project assets
 * This includes ONLY assets from public/asset folder
 */
export const getKnownProjectAssets = () => {
  return [
    // Assets from public/asset folder ONLY
    {
      path: '/asset/clifton-charter-bus.jpeg',
      filename: 'clifton-charter-bus.jpeg',
      type: 'image'
    },
    {
      path: '/asset/passaic-56-passenger-charter-bus.jpeg',
      filename: 'passaic-56-passenger-charter-bus.jpeg',
      type: 'image'
    },
    {
      path: '/asset/passaic-56-passenger_img-why-section-image.jpeg',
      filename: 'passaic-56-passenger_img-why-section-image.jpeg',
      type: 'image'
    }
    // Note: Only including public/asset folder, not public/img or others
  ];
};

/**
 * Discover all images in /asset/ folder by trying common filenames
 * This tries to find additional images beyond the known ones
 */
export const discoverAllAssetFolderImages = async () => {
  const discoveredAssets = [];

  // Common image file extensions
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'bmp', 'tiff'];

  // Try to discover images with common naming patterns
  const commonPatterns = [
    // Numbers
    '1', '2', '3', '4', '5', '6', '7', '8', '9', '10',
    // Common names
    'image', 'img', 'photo', 'picture', 'pic',
    'banner', 'hero', 'background', 'bg',
    'logo', 'icon', 'favicon',
    'header', 'footer', 'sidebar',
    'main', 'content', 'section',
    // Variations with numbers
    'image1', 'image2', 'img1', 'img2',
    'photo1', 'photo2', 'pic1', 'pic2'
  ];

  console.log('Discovering additional images in /asset/ folder...');

  for (const pattern of commonPatterns) {
    for (const ext of imageExtensions) {
      const assetPath = `/asset/${pattern}.${ext}`;
      try {
        const response = await fetch(assetPath, { method: 'HEAD' });
        if (response.ok) {
          const filename = `${pattern}.${ext}`;
          // Check if we already have this asset
          const knownAssets = getKnownProjectAssets();
          const alreadyKnown = knownAssets.some(asset => asset.filename === filename);

          if (!alreadyKnown) {
            discoveredAssets.push({
              path: assetPath,
              filename: filename,
              type: 'image'
            });
            console.log(`Discovered additional asset: ${filename}`);
          }
        }
      } catch (error) {
        // Asset doesn't exist, continue silently
      }
    }
  }

  console.log(`Discovered ${discoveredAssets.length} additional assets in /asset/ folder`);
  return discoveredAssets;
};



/**
 * Get asset replacement patterns for HTML content
 * Only handles /asset/ folder replacements
 */
export const getAssetReplacementPatterns = () => {
  const knownAssets = getKnownProjectAssets();
  const patterns = [];

  // Create replacement patterns for each known asset from /asset/ folder
  knownAssets.forEach(asset => {
    patterns.push({
      from: new RegExp(asset.path.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
      to: `./assets/${asset.filename}`
    });
  });

  // Add ONLY /asset/ folder replacement (removed other folders)
  patterns.push(
    { from: /\/asset\//g, to: './assets/' }
  );

  return patterns;
};

/**
 * Extract asset references from HTML content
 * This helps identify which assets are actually used
 */
export const extractAssetReferences = (htmlContent) => {
  const assetReferences = [];

  // Regular expressions to find asset references
  const patterns = [
    // img src attributes
    /src=["']([^"']*\.(jpg|jpeg|png|gif|svg|webp))[^"']*["']/gi,
    // CSS background images
    /background-image:\s*url\(["']?([^"')]*\.(jpg|jpeg|png|gif|svg|webp))[^"')]*["']?\)/gi,
    // link href for icons
    /href=["']([^"']*\.(ico|png|svg))[^"']*["']/gi
  ];

  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(htmlContent)) !== null) {
      const assetPath = match[1];
      if (assetPath && !assetReferences.includes(assetPath)) {
        assetReferences.push(assetPath);
      }
    }
  });

  return assetReferences;
};

/**
 * Validate if an asset exists and is accessible
 */
export const validateAsset = async (assetPath) => {
  try {
    const response = await fetch(assetPath, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    return false;
  }
};

/**
 * Get all assets that should be included in export
 * Combines known assets with discovered assets from /asset/ folder ONLY
 */
export const getAllExportAssets = async () => {
  console.log('Getting all assets from /asset/ folder only...');

  const knownAssets = getKnownProjectAssets();
  const discoveredAssets = await discoverAllAssetFolderImages();

  // Combine known and discovered assets
  const allAssets = [...knownAssets, ...discoveredAssets];

  // Validate assets exist and are accessible
  const validAssets = [];
  for (const asset of allAssets) {
    const isValid = await validateAsset(asset.path);
    if (isValid) {
      validAssets.push(asset);
      console.log(`✓ Validated asset: ${asset.filename}`);
    } else {
      console.warn(`✗ Could not validate asset: ${asset.path}`);
    }
  }

  console.log(`Total valid assets from /asset/ folder: ${validAssets.length}`);
  return validAssets;
};

/**
 * Create asset manifest for export
 */
export const createAssetManifest = (assets) => {
  return {
    version: '1.0.0',
    generatedAt: new Date().toISOString(),
    totalAssets: assets.length,
    assets: assets.map(asset => ({
      originalPath: asset.path,
      exportPath: `./assets/${asset.filename}`,
      filename: asset.filename,
      type: asset.type
    }))
  };
};
