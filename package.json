{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"antd": "^5.26.7", "archiver": "^7.0.1", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "express": "^5.1.0", "file-saver": "^2.0.5", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "lucide-react": "^0.344.0", "multer": "^2.0.2", "prismjs": "^1.30.0", "react": "^18.3.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-router-dom": "^7.7.1", "react-simple-code-editor": "^0.14.1", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}